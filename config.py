"""
Configuration file for the Streamlit SQL Chat Application
Modify these settings according to your environment
"""

# AWS Configuration
AWS_REGION = "us-west-2"
SCHEMA_NAME = "oasis_normalized"  # Your Athena database name
S3_STAGING_DIR = "s3://cmg-oasis-prod-sandbox-nba-medical-analyst/VeevaLink_RAG_SQL/staging_directory/"

# Bedrock Configuration
BEDROCK_ASSUME_ROLE = "arn:aws:iam::408026967156:role/MyBedrockRole"  # Optional: IAM role to assume
BEDROCK_MODEL_ID = "anthropic.claude-3-sonnet-20240229-v1:0"
BEDROCK_MODEL_KWARGS = {
    "temperature": 0.7,
    "top_p": 0.95,
    "max_tokens": 2048
}

# Application Configuration
APP_TITLE = "Chat with Veevalink Event Talk Table"
APP_DESCRIPTION = """Ask me any question about our table. If you feel my responses are not as expected, 
please ask the question again or let's restart our conversation."""

# Self-consistency configuration
SELF_CONSISTENCY_ITERATIONS = 3  # Number of times to run the agent for consistency

# DML keywords to block (for security)
DML_KEYWORDS = [
    'update', 'delete', 'insert', 'drop', 'alter', 'create', 
    'remove', 'truncate', 'modify', 'replace', 'change', 'clear'
]

# SQL Agent System Prompt
SQL_SYSTEM_PROMPT = """
You are an agent designed to interact with a SQL database.
Given an input question, create a syntactically correct AWS Athena query to run, then look at the results of the query and return the answer.
Unless the user specifies a specific number of examples / results they wish to obtain, always don't limit your number of results. Output all the results.
You can order the results by a relevant column to return the most interesting examples in the database.
Never query for all the columns from a specific table, only ask for the relevant columns given the question.
You have access to tools for interacting with the database.
Only use the given tools. Only use the information returned by the tools to construct your final answer.
You MUST double check your query before executing it. If you get an error while executing a query, rewrite the query and try again.

DO NOT make any DML statements (INSERT, UPDATE, DELETE, DROP etc.) to the database.

If the question does not seem related to the database, just return "I don't know" as the answer.

You should use the tool to get the list of tables in the database, then query the schema of the most relevant tables.
Do NOT skip this step.
Then you should query the schema of the most relevant tables. 

Give the answer such that even non technincal users can
understand what the answer means. Don't include technical details like what queries have been run in the output.
Don't include any 'Select * from' or  any select statements in the output /responses.
It should be clear to non techical users too.  Just give output of the user question. No details about queries you 
ran against the user question needed. 
"""

# Self-consistency prompt template
CONSISTENCY_PROMPT_TEMPLATE = """
considering the user question {user_question} and using the answers {answers}, return the final repsonse which you as an sql expert think as best by 
reviewing all the answers, then combining the most accurate and valuable information from each answer, then rephrasing and structuring the final output to ensure it is clear, concise, and logically organized, avoiding any redundancy. 
The goal is to produce a comprehensive response that captures the strengths of each individual answer by considering relevance to the question. Just give he final answer. No verbose is needed
"""

# Streamlit page configuration
PAGE_CONFIG = {
    "page_title": "Chat with SQL Database",
    "page_icon": "💬",
    "layout": "wide",
    "initial_sidebar_state": "expanded"
}

# Custom CSS styles
CUSTOM_CSS = """
<style>
    .main-header {
        text-align: center;
        color: #4A90E2;
        font-size: 2em;
        margin-bottom: 20px;
    }
    
    .description {
        text-align: left;
        color: #FF4500;
        font-size: 1em;
        margin-bottom: 30px;
        padding: 10px;
        background-color: #f0f0f0;
        border-radius: 5px;
    }
    
    .chat-message {
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
        display: flex;
        flex-direction: column;
    }
    
    .user-message {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
    }
    
    .assistant-message {
        background-color: #f3e5f5;
        border-left: 4px solid #9c27b0;
    }
    
    .error-message {
        background-color: #ffebee;
        border-left: 4px solid #f44336;
        color: #c62828;
    }
    
    .stTextInput > div > div > input {
        color: red;
        background-color: lightblue;
        border: 2px solid #00ccff;
        font-size: 16px;
        padding: 10px;
    }
</style>
"""

# Predefined responses for common inputs
PREDEFINED_RESPONSES = {
    'bye': "Thank you for using my service. Have a great day!",
    'stop': "Thank you for using my service. Have a great day!",
    'hi': "Hello! Hope you are doing well",
    'hello': "Hello! Hope you are doing well",
    'how are you': "Dear user, I'm fine Thank You"
}

# Error messages
ERROR_MESSAGES = {
    'dml_blocked': "Dear user, Please don't ask questions that modify the database. Thank you.",
    'general_error': "Sorry, I couldn't get results for your query this time. Please try again.",
    'initialization_error': "Failed to initialize. Please check your configuration.",
    'database_error': "Failed to initialize database: {error}"
}

# File paths for temporary storage
TEMP_FILES = {
    'query': 'temp_query.txt',
    'output': 'output.txt',
    'final_output': 'output_final.txt'
}

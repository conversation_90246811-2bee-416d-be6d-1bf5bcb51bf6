# SQL Agent with LangGraph

A sophisticated natural language to SQL query system that allows users to interact with databases using plain English. This agent translates questions into SQL queries, executes them, and provides human-readable answers.

## Table of Contents

- [Overview](#overview)
- [Installation](#installation)
- [Usage](#usage)
- [Architecture](#architecture)
- [Language Model](#language-model)
- [Troubleshooting](#troubleshooting)
- [Contributing](#contributing)

## Overview

This SQL agent combines several powerful technologies:

- **LangGraph**: For orchestrating the workflow between language understanding and database querying
- **Self-Consistency**: Multiple query attempts are combined for more reliable results
- **Gradio Interface**: Provides an intuitive chat interface for users
- **Claude 3.7 Sonnet**: Anthrop<PERSON>'s advanced language model accessed via AWS Bedrock

The system allows non-technical users to query databases without knowing SQL syntax, making data more accessible across your organization.

## Installation

### Prerequisites

- Python 3.9+
- AWS account with Bedrock access (for Claude model)
- Access to the target SQL database

### Setup

1. Clone this repository:
   ```bash
   git clone <repository-url>
   cd sql-agent-langgraph
   ```

2. Install dependencies:
   ```bash
   pip install -U langchain SQLAlchemy pyathena boto3 s3fs langgraph gradio==5.6.0 langchain-community awswrangler langchain_experimental langchain_aws langgraph-checkpoint-sqlite
   ```

3. Configure AWS credentials:
   ```bash
   export AWS_ACCESS_KEY_ID=your_access_key
   export AWS_SECRET_ACCESS_KEY=your_secret_key
   export AWS_REGION=us-west-2
   ```

4. Update database connection details in your environment or configuration file.

## Usage

### Starting the Interface

Run the application:

```bash
python notebook_integration.py
```

This will start a Gradio web interface, typically accessible at http://127.0.0.1:7860.

### Example Queries

You can ask questions like:

- "How many events were held in 2023?"
- "What are the top 5 speakers by number of talks?"
- "Show me all events related to oncology in the last quarter"

The system will translate these into SQL queries, execute them, and return formatted results.

## Architecture

### Core Components

1. **LangGraph Workflow**
   - **Agent Node**: Processes user questions and decides on actions
   - **Tool Node**: Executes database operations
   - The workflow alternates between these nodes until a complete answer is ready

2. **Self-Consistency Mechanism**
   - Runs each query multiple times (3 by default)
   - Captures different responses and SQL queries
   - Uses a "consistency chain" to combine the best parts of each attempt
   - Produces a refined final answer

3. **Gradio Interface**
   - Provides a chat-like experience for users
   - Displays both the SQL query and the natural language answer
   - Compares agent responses with direct language model responses

### Data Flow

1. User submits a question through the Gradio interface
2. The question is processed by the LangGraph agent
3. The agent generates SQL queries and executes them against the database
4. Results are processed and formatted into a human-readable response
5. The response is displayed to the user in the chat interface

## Language Model

### Claude 3.7 Sonnet

Our SQL agent is powered by Anthropic's Claude 3.7 Sonnet model, accessed through AWS Bedrock. This model was chosen for several key reasons:

#### Capabilities

- **Natural Language Understanding**: Excels at interpreting complex, ambiguous user queries
- **SQL Generation**: Produces accurate, syntactically correct SQL queries
- **Context Awareness**: Maintains understanding of database schema and previous interactions
- **Clear Explanations**: Provides human-readable summaries of technical database results

#### Configuration

We've configured Claude 3.7 Sonnet with the following parameters:

```python
anthropic_llm = ChatBedrock(
    model_id="anthropic.claude-3-sonnet-20240229-v1:0",
    model_kwargs={
        "temperature": 0.7,  # Balanced creativity and determinism
        "top_p": 0.95,       # Diverse yet focused responses
        "max_tokens": 2048   # Sufficient space for detailed answers
    }
)
```

These settings provide an optimal balance between:
- Consistency in SQL generation (lower temperature)
- Flexibility in natural language responses (moderate temperature)
- Comprehensive explanations (adequate token limit)

#### Performance Characteristics

- **Accuracy**: Consistently generates correct SQL for a wide range of query types
- **Robustness**: Handles edge cases and ambiguous questions effectively
- **Efficiency**: Processes queries quickly with minimal latency
- **Learning**: Adapts to the specific database schema through few-shot examples

The Claude 3.7 Sonnet model serves as both the brain of our agent system and the communication layer that makes complex database information accessible to all users.

## Troubleshooting

### Common Issues

#### Recursion Limit Errors

If you see `GraphRecursionError: Recursion limit reached without hitting a stop condition`:

- Increase the recursion limit in the configuration:
  ```python
  config = {"configurable": {"thread_id": thread_id}, "recursion_limit": 75}
  ```

#### Tool Serialization Errors

If you encounter `Object is not JSON serializable` errors:

- Check that all tools are properly initialized
- Ensure the ToolExecutor class is handling your specific tools correctly

#### AWS Authentication Issues

For AWS Bedrock authentication problems:

- Verify your AWS credentials are correctly set
- Check that your IAM role has appropriate permissions for Bedrock
- Ensure the correct region is specified

#### Database Connection Problems

If database queries fail:

- Verify connection string and credentials
- Check network connectivity to the database
- Ensure SQL permissions are correctly set

#### LLM Response Issues

If Claude 3.7 Sonnet responses are unexpected:

- Check that the model ID is correctly specified
- Adjust temperature settings (lower for more deterministic responses)
- Ensure your AWS account has access to the Claude 3.7 Sonnet model
- Verify that your prompts include clear instructions and context

### Debugging Tips

1. Enable verbose logging to see detailed execution flow
2. Check the captured stdout in the `temp_query.txt` file
3. Review the final output in `output_final.txt`
4. For complex issues, try running individual components separately

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

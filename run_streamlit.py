#!/usr/bin/env python3
"""
Simple launcher script for the Streamlit SQL Chat Application
This script handles basic setup and launches the Streamlit app
"""

import os
import sys
import subprocess
import importlib.util

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'streamlit',
        'langchain',
        'boto3',
        'sqlalchemy',
        'pyathena'
    ]
    
    missing_packages = []
    for package in required_packages:
        spec = importlib.util.find_spec(package)
        if spec is None:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n📦 Please install missing packages:")
        print("   pip install -r requirements_streamlit.txt")
        return False
    
    print("✅ All required packages are installed")
    return True

def check_config():
    """Check if configuration file exists and is properly set up"""
    if not os.path.exists('config.py'):
        print("❌ Configuration file 'config.py' not found")
        print("📝 Please ensure config.py exists in the current directory")
        return False
    
    try:
        import config
        required_vars = ['AWS_REGION', 'SCHEMA_NAME', 'S3_STAGING_DIR']
        for var in required_vars:
            if not hasattr(config, var):
                print(f"❌ Missing configuration variable: {var}")
                return False
        print("✅ Configuration file is properly set up")
        return True
    except ImportError as e:
        print(f"❌ Error importing configuration: {e}")
        return False

def check_aws_credentials():
    """Check if AWS credentials are available"""
    # Check environment variables
    if 'AWS_ACCESS_KEY_ID' in os.environ and 'AWS_SECRET_ACCESS_KEY' in os.environ:
        print("✅ AWS credentials found in environment variables")
        return True
    
    # Check AWS credentials file
    aws_creds_file = os.path.expanduser('~/.aws/credentials')
    if os.path.exists(aws_creds_file):
        print("✅ AWS credentials file found")
        return True
    
    # Check if running on EC2 with IAM role
    try:
        import boto3
        session = boto3.Session()
        credentials = session.get_credentials()
        if credentials:
            print("✅ AWS credentials available (possibly from IAM role)")
            return True
    except Exception:
        pass
    
    print("⚠️  AWS credentials not found")
    print("   Please set up AWS credentials using one of these methods:")
    print("   1. Run 'aws configure'")
    print("   2. Set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY environment variables")
    print("   3. Use IAM role (if running on EC2)")
    return False

def main():
    """Main launcher function"""
    print("🚀 Starting Streamlit SQL Chat Application")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check configuration
    if not check_config():
        sys.exit(1)
    
    # Check AWS credentials (warning only, not blocking)
    check_aws_credentials()
    
    print("\n🌟 Launching Streamlit application...")
    print("   The application will open in your default browser")
    print("   Press Ctrl+C to stop the application")
    print("=" * 50)
    
    # Launch Streamlit
    try:
        subprocess.run([
            sys.executable, '-m', 'streamlit', 'run', 'streamlit_app.py',
            '--server.headless', 'false',
            '--server.runOnSave', 'true',
            '--browser.gatherUsageStats', 'false'
        ])
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"\n❌ Error launching application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

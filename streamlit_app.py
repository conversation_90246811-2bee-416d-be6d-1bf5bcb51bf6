import streamlit as st
import warnings
import os
import sys
import json
import pandas as pd
import re
import boto3
import awswrangler as wr
from pyathena import connect
from io import StringIO
import traceback
import random
import string

# Import configuration
from config import *

# Suppress warnings
warnings.filterwarnings('ignore')

# LangChain imports
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain.chains.summarize import load_summarize_chain
from langchain_community.llms.bedrock import Bedrock
from langchain.llms import OpenAI
from langchain.chat_models import ChatOpenAI
from langchain.docstore.document import Document
from langchain.embeddings import BedrockEmbeddings
from langchain.chains import RetrievalQA
from langchain.vectorstores import FAISS
from langchain.chains import create_sql_query_chain
from langchain_experimental.sql import SQLDatabaseChain
from langchain.utilities.sql_database import SQLDatabase
from sqlalchemy import create_engine
from langchain_community.document_loaders.athena import <PERSON><PERSON><PERSON><PERSON>
from langchain_community.tools.sql_database.tool import QuerySQLDataBaseTool
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langgraph.prebuilt import create_react_agent
from langchain_core.output_parsers import StrOutputParser

# Additional imports
from urllib.parse import quote_plus
from langchain_community.utilities import SQLDatabase
from langchain_core.runnables import RunnablePassthrough
from langchain_community.chat_models import BedrockChat
from langchain_aws.chat_models.bedrock import ChatBedrock
from langchain_core.prompts import PromptTemplate
from langgraph.checkpoint.sqlite import SqliteSaver
from langgraph.checkpoint.memory import MemorySaver
from langchain_core.output_parsers.openai_tools import PydanticToolsParser
from pydantic import BaseModel, Field

# Custom imports (assuming these exist in your workspace)
try:
    module_path = ".."
    sys.path.append(os.path.abspath(module_path))
    from utils import bedrock, print_ww
except ImportError:
    st.warning("Utils module not found. Some functionality may be limited.")

# Try to import custom SQLDatabaseToolkit, fallback to standard if not available
try:
    from lc_community.agent_toolkits.new_toolkit import SQLDatabaseToolkit
except ImportError:
    try:
        from langchain_community.agent_toolkits import SQLDatabaseToolkit
        st.info("Using standard SQLDatabaseToolkit. If you have custom toolkit, please ensure lc_community module is available.")
    except ImportError:
        st.error("SQLDatabaseToolkit not found. Please install langchain-community.")
        st.stop()

# Streamlit page configuration
st.set_page_config(**PAGE_CONFIG)

# Custom CSS styling
st.markdown(CUSTOM_CSS, unsafe_allow_html=True)

# Initialize session state
if 'messages' not in st.session_state:
    st.session_state.messages = []

if 'db_initialized' not in st.session_state:
    st.session_state.db_initialized = False

if 'agent_executor' not in st.session_state:
    st.session_state.agent_executor = None

if 'consistency_chain' not in st.session_state:
    st.session_state.consistency_chain = None

# Configuration is now imported from config.py

def generate_random_string():
    """Generate a random string for thread IDs"""
    ascii_values = list(range(97, 121))
    random_char = random.choice(ascii_values)
    random_string = chr(random_char) + chr(random_char + 1) + chr(random_char + 2)
    number_list = [1, 2, 3, 4, 5, 6, 7]
    random_int = random.choice(number_list)
    random_number = str(random_int) + str(random_int + 1) + str(random_int + 2)
    return random_string + random_number

@st.cache_resource
def initialize_database():
    """Initialize database connection and LLM"""
    try:
        # Set up environment variables
        if BEDROCK_ASSUME_ROLE:
            os.environ["BEDROCK_ASSUME_ROLE"] = BEDROCK_ASSUME_ROLE
        os.environ["AWS_DEFAULT_REGION"] = AWS_REGION
        
        # Create database connection
        connect_str = "awsathena+rest://athena.{region_name}.amazonaws.com:443/{schema_name}?s3_staging_dir={s3_staging_dir}"
        engine = create_engine(connect_str.format(
            region_name=AWS_REGION,
            schema_name=SCHEMA_NAME,
            s3_staging_dir=quote_plus(S3_STAGING_DIR)
        ))
        
        db = SQLDatabase(engine)
        
        # Initialize Bedrock client
        bedrock_client = boto3.client('bedrock-runtime', region_name=AWS_REGION)
        
        # Initialize LLM
        anthropic_llm = ChatBedrock(
            model_id=BEDROCK_MODEL_ID,
            model_kwargs=BEDROCK_MODEL_KWARGS,
        )
        
        # Create tools
        toolkit = SQLDatabaseToolkit(db=db, llm=anthropic_llm)
        tools = toolkit.get_tools()
        
        # Create system message
        system_message = SystemMessage(content=SQL_SYSTEM_PROMPT)
        
        # Create memory and agent
        memory = MemorySaver()
        agent_executor = create_react_agent(anthropic_llm, tools, prompt=system_message, checkpointer=memory)
        
        # Create consistency chain
        consistency_prompt = PromptTemplate.from_template(CONSISTENCY_PROMPT_TEMPLATE)
        consistency_chain = consistency_prompt | anthropic_llm
        
        return db, anthropic_llm, agent_executor, consistency_chain, tools
        
    except Exception as e:
        st.error(f"Failed to initialize database: {str(e)}")
        return None, None, None, None, None

def ask_sql_with_memory_self_consistency_query(user_input):
    """Main function to process SQL queries with self-consistency"""
    # Check for predefined responses
    user_lower = user_input.lower()
    for key, response in PREDEFINED_RESPONSES.items():
        if key in user_lower:
            return response

    # Check for DML keywords
    if any(keyword in user_lower for keyword in DML_KEYWORDS):
        return ERROR_MESSAGES['dml_blocked']
    
    try:
        responses = []
        query_outputs = set()
        old_stdout = sys.stdout
        
        # Run the agent multiple times for self-consistency
        for j in range(SELF_CONSISTENCY_ITERATIONS):
            mystdout = StringIO()
            sys.stdout = mystdout
            
            config = {"configurable": {"thread_id": generate_random_string()}}
            res = st.session_state.agent_executor.invoke(
                {"messages": [HumanMessage(content=user_input)]}, 
                config
            )
            responses.append(res['messages'][-1].content)
            
            sys.stdout = old_stdout
            captured_output = mystdout.getvalue()
            captured_output = captured_output.replace('\n\n', '\n').replace('\n', ' ').replace('  ', ' ')
            captured_output = captured_output.strip()
            
            # Save captured output
            with open(TEMP_FILES['query'], 'w') as file:
                file.write(captured_output)
            with open(TEMP_FILES['query'], 'r') as file:
                captured_output = file.read()
            
            query_outputs.add(captured_output.strip())
            sys.stdout = mystdout
        
        sys.stdout = old_stdout
        
        # Combine responses using consistency chain
        answers = '\n\n'.join(responses)
        final_res = st.session_state.consistency_chain.invoke({
            "answers": answers, 
            'user_question': user_input
        })
        final_res = final_res.content
        final_res = '\n'.join(item.strip() for item in final_res.replace('\n\n', '\n').split('\n'))
        
        # Save final result
        with open(TEMP_FILES['output'], 'w') as file:
            file.write(final_res)
        with open(TEMP_FILES['output'], 'r') as file:
            content = file.read()
        
        # Get direct LLM response for comparison
        llm_response = st.session_state.anthropic_llm.invoke(user_input).content
        
        # Format final result
        final_result = (
            'Agent Response is : \n' + 
            list(query_outputs)[-1] + 
            '\n and \n' + 
            content.replace('veeva_link_event_talk_table table', 'table').replace('veeva_link_event_talk_table', 'table') + 
            '\n\nLLM Response is : \n' + 
            llm_response
        )
        
        with open(TEMP_FILES['final_output'], 'w') as file:
            file.write(final_result)

        return final_result

    except Exception as e:
        error_msg = f"An error occurred: {str(e)}\n"
        error_msg += traceback.format_exc()
        return f"{ERROR_MESSAGES['general_error']}\n\nError details: {error_msg}"

# Main Streamlit app
def main():
    # Header
    st.markdown(f'<div class="main-header">{APP_TITLE}</div>', unsafe_allow_html=True)

    # Description
    st.markdown(f'''
    <div class="description">
    {APP_DESCRIPTION}
    </div>
    ''', unsafe_allow_html=True)
    
    # Initialize database if not already done
    if not st.session_state.db_initialized:
        with st.spinner("Initializing database connection and AI models..."):
            db, anthropic_llm, agent_executor, consistency_chain, tools = initialize_database()
            
            if db is not None:
                st.session_state.db = db
                st.session_state.anthropic_llm = anthropic_llm
                st.session_state.agent_executor = agent_executor
                st.session_state.consistency_chain = consistency_chain
                st.session_state.tools = tools
                st.session_state.db_initialized = True
                st.success("Database and AI models initialized successfully!")
            else:
                st.error(ERROR_MESSAGES['initialization_error'])
                st.stop()
    
    # Sidebar with information
    with st.sidebar:
        st.header("ℹ️ Information")
        st.write("**Database:** AWS Athena")
        st.write("**Schema:** oasis_normalized")
        st.write("**AI Model:** Claude 3 Sonnet")
        
        st.header("🚫 Restrictions")
        st.write("- No DML operations (INSERT, UPDATE, DELETE, etc.)")
        st.write("- Database queries only")
        
        st.header("💡 Tips")
        st.write("- Ask specific questions about the data")
        st.write("- Use natural language")
        st.write("- Be patient - complex queries may take time")
        
        if st.button("🗑️ Clear Chat History"):
            st.session_state.messages = []
            st.rerun()
    
    # Display chat messages
    for message in st.session_state.messages:
        if message["role"] == "user":
            st.markdown(f'''
            <div class="chat-message user-message">
                <strong>You:</strong><br>
                {message["content"]}
            </div>
            ''', unsafe_allow_html=True)
        else:
            st.markdown(f'''
            <div class="chat-message assistant-message">
                <strong>Assistant:</strong><br>
                {message["content"]}
            </div>
            ''', unsafe_allow_html=True)
    
    # Chat input
    if prompt := st.chat_input("Hello... Drop Your Question here"):
        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": prompt})
        
        # Display user message
        st.markdown(f'''
        <div class="chat-message user-message">
            <strong>You:</strong><br>
            {prompt}
        </div>
        ''', unsafe_allow_html=True)
        
        # Generate response
        with st.spinner("Thinking..."):
            response = ask_sql_with_memory_self_consistency_query(prompt)
        
        # Add assistant response to chat history
        st.session_state.messages.append({"role": "assistant", "content": response})
        
        # Display assistant response
        st.markdown(f'''
        <div class="chat-message assistant-message">
            <strong>Assistant:</strong><br>
            {response}
        </div>
        ''', unsafe_allow_html=True)
        
        # Rerun to update the display
        st.rerun()

if __name__ == "__main__":
    main()

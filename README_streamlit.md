# Streamlit SQL Chat Application

This is a Streamlit conversion of the original Gradio-based SQL chat application. The application provides a conversational interface to query SQL databases using natural language, powered by AWS Bedrock's Claude 3 Sonnet model.

## Features

- **Natural Language SQL Queries**: Ask questions in plain English and get SQL results
- **Self-Consistency**: Runs multiple queries and combines results for better accuracy
- **AWS Athena Integration**: Connects to AWS Athena databases
- **Memory Management**: Maintains conversation context
- **Safety Features**: Blocks DML operations (INSERT, UPDATE, DELETE, etc.)
- **Real-time Chat Interface**: Modern chat UI with message history

## Prerequisites

1. **AWS Account**: You need access to AWS Bedrock and Athena
2. **AWS Credentials**: Properly configured AWS credentials
3. **Python 3.8+**: Python environment with pip
4. **Database Access**: Access to the target Athena database

## Installation

1. **Clone or download the files**:
   ```bash
   # Ensure you have these files:
   # - streamlit_app.py
   # - requirements_streamlit.txt
   # - README_streamlit.md
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements_streamlit.txt
   ```

3. **Set up AWS credentials** (choose one method):
   
   **Method A: AWS CLI**
   ```bash
   aws configure
   ```
   
   **Method B: Environment Variables**
   ```bash
   export AWS_ACCESS_KEY_ID=your_access_key
   export AWS_SECRET_ACCESS_KEY=your_secret_key
   export AWS_DEFAULT_REGION=us-west-2
   ```
   
   **Method C: IAM Role** (if running on EC2)
   - Attach appropriate IAM role to your EC2 instance

4. **Configure the application**:
   
   Edit the configuration constants in `streamlit_app.py`:
   ```python
   AWS_REGION = "us-west-2"  # Change to your region
   SCHEMA_NAME = "your_database_name"  # Change to your database
   S3_STAGING_DIR = "s3://your-bucket/staging/"  # Change to your S3 bucket
   ```

## Required AWS Permissions

Your AWS credentials need the following permissions:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "bedrock:InvokeModel",
                "bedrock:InvokeModelWithResponseStream"
            ],
            "Resource": "arn:aws:bedrock:*:*:model/anthropic.claude-3-sonnet-20240229-v1:0"
        },
        {
            "Effect": "Allow",
            "Action": [
                "athena:StartQueryExecution",
                "athena:GetQueryExecution",
                "athena:GetQueryResults",
                "athena:StopQueryExecution",
                "athena:GetWorkGroup"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "s3:GetObject",
                "s3:PutObject",
                "s3:DeleteObject",
                "s3:ListBucket"
            ],
            "Resource": [
                "arn:aws:s3:::your-staging-bucket/*",
                "arn:aws:s3:::your-staging-bucket"
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "glue:GetTable",
                "glue:GetTables",
                "glue:GetDatabase",
                "glue:GetDatabases",
                "glue:GetPartitions"
            ],
            "Resource": "*"
        }
    ]
}
```

## Running the Application

1. **Start the Streamlit app**:
   ```bash
   streamlit run streamlit_app.py
   ```

2. **Access the application**:
   - Open your browser to `http://localhost:8501`
   - The app will initialize database connections automatically

3. **Start chatting**:
   - Type your questions in natural language
   - Example: "What are the top 10 customers by revenue?"
   - The AI will convert your question to SQL and return results

## Key Differences from Gradio Version

### UI Components
- **Gradio**: `gr.ChatInterface()` with `gr.Chatbot()` and `gr.Textbox()`
- **Streamlit**: `st.chat_input()` with custom message display using `st.markdown()`

### State Management
- **Gradio**: Automatic history parameter in chat function
- **Streamlit**: Manual state management using `st.session_state`

### Styling
- **Gradio**: CSS passed to `css` parameter
- **Streamlit**: CSS injected using `st.markdown()` with `unsafe_allow_html=True`

### Layout
- **Gradio**: Single interface with built-in components
- **Streamlit**: Custom layout with sidebar, main area, and manual message rendering

## Troubleshooting

### Common Issues

1. **"Custom SQLDatabaseToolkit not found"**:
   - The app will fallback to standard toolkit
   - If you have custom toolkit, ensure `lc_community` module is in your path

2. **"Failed to initialize database"**:
   - Check AWS credentials
   - Verify database name and S3 staging directory
   - Ensure proper permissions

3. **"InvalidChecksumConfigError"**:
   - This is handled automatically in the code
   - If issues persist, update boto3/botocore versions

4. **Slow responses**:
   - This is normal for complex queries
   - The app runs 3 iterations for self-consistency
   - Consider reducing iterations in the code if needed

### Performance Optimization

1. **Reduce self-consistency iterations**:
   ```python
   # In ask_sql_with_memory_self_consistency_query function
   for j in range(1):  # Change from range(3) to range(1)
   ```

2. **Cache database connection**:
   - Already implemented using `@st.cache_resource`

3. **Optimize model parameters**:
   ```python
   anthropic_llm = ChatBedrock(
       model_id="anthropic.claude-3-sonnet-20240229-v1:0",
       model_kwargs={"temperature": 0.3, "top_p": 0.8, 'max_tokens': 1024},  # Reduced values
   )
   ```

## Customization

### Changing the Model
```python
# In initialize_database() function
anthropic_llm = ChatBedrock(
    model_id="anthropic.claude-3-haiku-20240307-v1:0",  # Faster, cheaper model
    model_kwargs={"temperature": 0.7, "top_p": 0.95, 'max_tokens': 2048},
)
```

### Modifying the UI
```python
# Change page configuration
st.set_page_config(
    page_title="Your Custom Title",
    page_icon="🔍",  # Change icon
    layout="centered",  # or "wide"
)

# Modify styling in the CSS section
```

### Adding Features
- Add file upload for CSV queries
- Implement query history export
- Add visualization capabilities
- Include query performance metrics

## Security Considerations

1. **Input Validation**: The app blocks DML operations
2. **AWS Permissions**: Use least-privilege principle
3. **Network Security**: Consider VPC endpoints for AWS services
4. **Data Privacy**: Ensure compliance with data governance policies

## Support

For issues related to:
- **Streamlit**: Check [Streamlit documentation](https://docs.streamlit.io/)
- **LangChain**: Check [LangChain documentation](https://python.langchain.com/)
- **AWS Bedrock**: Check [AWS Bedrock documentation](https://docs.aws.amazon.com/bedrock/)
- **AWS Athena**: Check [AWS Athena documentation](https://docs.aws.amazon.com/athena/)

## Migration Notes

This Streamlit version maintains the same core functionality as the original Gradio version:
- Same SQL agent logic
- Same self-consistency approach
- Same AWS integrations
- Same safety features

The main changes are in the user interface and state management to work with Streamlit's architecture.

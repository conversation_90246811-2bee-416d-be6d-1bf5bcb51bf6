{"cells": [{"cell_type": "code", "execution_count": 1, "id": "fbfb7c31-20ae-4c9f-a91e-72cd2e8580ed", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting pydantic==2.9.2\n", "  Using cached pydantic-2.9.2-py3-none-any.whl.metadata (149 kB)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /opt/conda/lib/python3.12/site-packages (from pydantic==2.9.2) (0.7.0)\n", "Collecting pydantic-core==2.23.4 (from pydantic==2.9.2)\n", "  Using cached pydantic_core-2.23.4-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.6 kB)\n", "Requirement already satisfied: typing-extensions>=4.6.1 in /opt/conda/lib/python3.12/site-packages (from pydantic==2.9.2) (4.12.2)\n", "Using cached pydantic-2.9.2-py3-none-any.whl (434 kB)\n", "Using cached pydantic_core-2.23.4-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.1 MB)\n", "Installing collected packages: pydantic-core, pydantic\n", "  Attempting uninstall: pydantic-core\n", "    Found existing installation: pydantic_core 2.27.2\n", "    Uninstalling pydantic_core-2.27.2:\n", "      Successfully uninstalled pydantic_core-2.27.2\n", "  Attempting uninstall: pydantic\n", "    Found existing installation: pydantic 2.10.6\n", "    Uninstalling pydantic-2.10.6:\n", "      Successfully uninstalled pydantic-2.10.6\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "jupyter-ai 2.30.0 requires faiss-cpu!=1.8.0.post0,<2.0.0,>=1.8.0, which is not installed.\n", "jupyter-ai 2.30.0 requires pydantic<3,>=2.10.0, but you have pydantic 2.9.2 which is incompatible.\n", "jupyter-ai-magics 2.30.0 requires pydantic<3,>=2.10.0, but you have pydantic 2.9.2 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed pydantic-2.9.2 pydantic-core-2.23.4\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install pydantic==2.9.2"]}, {"cell_type": "code", "execution_count": 2, "id": "ea19acac-5ba3-48b5-9f45-3f74706314de", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting langchain\n", "  Using cached langchain-0.3.25-py3-none-any.whl (1.0 MB)\n", "Collecting SQLAlchemy\n", "  Downloading sqlalchemy-2.0.41-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.3 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.3/3.3 MB\u001b[0m \u001b[31m63.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting pyathena\n", "  Downloading pyathena-3.14.1-py3-none-any.whl (76 kB)\n", "Collecting boto3\n", "  Downloading boto3-1.38.36-py3-none-any.whl (139 kB)\n", "Collecting s3fs\n", "  Downloading s3fs-2025.5.1-py3-none-any.whl (30 kB)\n", "Collecting langgraph\n", "  Downloading langgraph-0.4.8-py3-none-any.whl (152 kB)\n", "Processing /home/<USER>/.cache/pip/wheels/0c/12/fe/d85bccc454625c939b6a33e67b0c85649d3b3a7b7ba7e13c1d/amazon_codewhisperer_jupyterlab_ext-2.0.2-py3-none-any.whl\n", "Collecting gradio==5.6.0\n", "  Using cached gradio-5.6.0-py3-none-any.whl (57.1 MB)\n", "Collecting langchain-community\n", "  Downloading langchain_community-0.3.25-py3-none-any.whl (2.5 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.5/2.5 MB\u001b[0m \u001b[31m160.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting awswrangler\n", "  Downloading awswrangler-3.12.0-py3-none-any.whl (379 kB)\n", "Collecting langchain_experimental\n", "  Using cached langchain_experimental-0.3.4-py3-none-any.whl (209 kB)\n", "Collecting langchain_aws\n", "  Downloading langchain_aws-0.2.25-py3-none-any.whl (120 kB)\n", "Collecting langgraph-checkpoint-sqlite\n", "  Downloading langgraph_checkpoint_sqlite-2.0.10-py3-none-any.whl (30 kB)\n", "Collecting langchain-core<1.0.0,>=0.3.58 (from langchain)\n", "  Downloading langchain_core-0.3.65-py3-none-any.whl (438 kB)\n", "Collecting langchain-text-splitters<1.0.0,>=0.3.8 (from langchain)\n", "  Using cached langchain_text_splitters-0.3.8-py3-none-any.whl (32 kB)\n", "Requirement already satisfied, skipping upgrade: langsmith<0.4,>=0.1.17 in /opt/conda/lib/python3.12/site-packages (from langchain) (0.2.11)\n", "Requirement already satisfied, skipping upgrade: pydantic<3.0.0,>=2.7.4 in /opt/conda/lib/python3.12/site-packages (from langchain) (2.9.2)\n", "Requirement already satisfied, skipping upgrade: requests<3,>=2 in /opt/conda/lib/python3.12/site-packages (from langchain) (2.32.3)\n", "Requirement already satisfied, skipping upgrade: PyYAML>=5.3 in /opt/conda/lib/python3.12/site-packages (from langchain) (6.0.2)\n", "Requirement already satisfied, skipping upgrade: greenlet>=1; python_version < \"3.14\" and (platform_machine == \"aarch64\" or (platform_machine == \"ppc64le\" or (platform_machine == \"x86_64\" or (platform_machine == \"amd64\" or (platform_machine == \"AMD64\" or (platform_machine == \"win32\" or platform_machine == \"WIN32\")))))) in /opt/conda/lib/python3.12/site-packages (from SQLAlchemy) (3.1.1)\n", "Requirement already satisfied, skipping upgrade: typing-extensions>=4.6.0 in /opt/conda/lib/python3.12/site-packages (from SQLAlchemy) (4.12.2)\n", "Requirement already satisfied, skipping upgrade: botocore>=1.29.4 in /opt/conda/lib/python3.12/site-packages (from pyathena) (1.37.1)\n", "Requirement already satisfied, skipping upgrade: fsspec in /opt/conda/lib/python3.12/site-packages (from pyathena) (2024.10.0)\n", "Requirement already satisfied, skipping upgrade: python-dateutil in /opt/conda/lib/python3.12/site-packages (from pyathena) (2.9.0.post0)\n", "Requirement already satisfied, skipping upgrade: tenacity>=4.1.0 in /opt/conda/lib/python3.12/site-packages (from pyathena) (9.0.0)\n", "Requirement already satisfied, skipping upgrade: jmespath<2.0.0,>=0.7.1 in /opt/conda/lib/python3.12/site-packages (from boto3) (1.0.1)\n", "Collecting s3transfer<0.14.0,>=0.13.0 (from boto3)\n", "  Downloading s3transfer-0.13.0-py3-none-any.whl (85 kB)\n", "Requirement already satisfied, skipping upgrade: aiobotocore<3.0.0,>=2.5.4 in /opt/conda/lib/python3.12/site-packages (from s3fs) (2.21.1)\n", "Requirement already satisfied, skipping upgrade: aiohttp!=4.0.0a0,!=4.0.0a1 in /opt/conda/lib/python3.12/site-packages (from s3fs) (3.9.5)\n", "Collecting langgraph-checkpoint>=2.0.26 (from langgraph)\n", "  Downloading langgraph_checkpoint-2.0.26-py3-none-any.whl (44 kB)\n", "Collecting langgraph-prebuilt>=0.2.0 (from langgraph)\n", "  Downloading langgraph_prebuilt-0.2.2-py3-none-any.whl (23 kB)\n", "Collecting langgraph-sdk>=0.1.42 (from langgraph)\n", "  Downloading langgraph_sdk-0.1.70-py3-none-any.whl (49 kB)\n", "Requirement already satisfied, skipping upgrade: xxhash>=3.5.0 in /opt/conda/lib/python3.12/site-packages (from langgraph) (3.5.0)\n", "Requirement already satisfied, skipping upgrade: jupyterlab<5.0,>=4.0 in /opt/conda/lib/python3.12/site-packages (from amazon-codewhisperer-jupyterlab-ext) (4.3.6)\n", "Collecting aiofiles<24.0,>=22.0 (from gradio==5.6.0)\n", "  Using cached aiofiles-23.2.1-py3-none-any.whl (15 kB)\n", "Requirement already satisfied, skipping upgrade: anyio<5.0,>=3.0 in /opt/conda/lib/python3.12/site-packages (from gradio==5.6.0) (4.9.0)\n", "Requirement already satisfied, skipping upgrade: fastapi<1.0,>=0.115.2 in /opt/conda/lib/python3.12/site-packages (from gradio==5.6.0) (0.115.11)\n", "Collecting ffmpy (from gradio==5.6.0)\n", "  Downloading ffmpy-0.6.0-py3-none-any.whl (5.5 kB)\n", "Collecting gradio-client==1.4.3 (from gradio==5.6.0)\n", "  Using cached gradio_client-1.4.3-py3-none-any.whl (320 kB)\n", "Requirement already satisfied, skipping upgrade: httpx>=0.24.1 in /opt/conda/lib/python3.12/site-packages (from gradio==5.6.0) (0.28.1)\n", "Requirement already satisfied, skipping upgrade: huggingface-hub>=0.25.1 in /opt/conda/lib/python3.12/site-packages (from gradio==5.6.0) (0.29.3)\n", "Requirement already satisfied, skipping upgrade: jinja2<4.0 in /opt/conda/lib/python3.12/site-packages (from gradio==5.6.0) (3.1.6)\n", "Collecting markupsafe~=2.0 (from gradio==5.6.0)\n", "  Using cached MarkupSafe-2.1.5-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (28 kB)\n", "Requirement already satisfied, skipping upgrade: numpy<3.0,>=1.0 in /opt/conda/lib/python3.12/site-packages (from gradio==5.6.0) (1.26.4)\n", "Requirement already satisfied, skipping upgrade: orjson~=3.0 in /opt/conda/lib/python3.12/site-packages (from gradio==5.6.0) (3.10.15)\n", "Requirement already satisfied, skipping upgrade: packaging in /opt/conda/lib/python3.12/site-packages (from gradio==5.6.0) (24.2)\n", "Requirement already satisfied, skipping upgrade: pandas<3.0,>=1.0 in /opt/conda/lib/python3.12/site-packages (from gradio==5.6.0) (2.2.3)\n", "Requirement already satisfied, skipping upgrade: pillow<12.0,>=8.0 in /opt/conda/lib/python3.12/site-packages (from gradio==5.6.0) (11.1.0)\n", "Collecting pydub (from gradio==5.6.0)\n", "  Using cached pydub-0.25.1-py2.py3-none-any.whl (32 kB)\n", "Collecting python-multipart==0.0.12 (from gradio==5.6.0)\n", "  Using cached python_multipart-0.0.12-py3-none-any.whl (23 kB)\n", "Collecting ruff>=0.2.2; sys_platform != \"emscripten\" (from gradio==5.6.0)\n", "  Downloading ruff-0.11.13-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (11.6 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m11.6/11.6 MB\u001b[0m \u001b[31m192.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting safehttpx<1.0,>=0.1.1 (from gradio==5.6.0)\n", "  Using cached safehttpx-0.1.6-py3-none-any.whl (8.7 kB)\n", "Collecting semantic-version~=2.0 (from gradio==5.6.0)\n", "  Using cached semantic_version-2.10.0-py2.py3-none-any.whl (15 kB)\n", "Requirement already satisfied, skipping upgrade: starlette<1.0,>=0.40.0; sys_platform != \"emscripten\" in /opt/conda/lib/python3.12/site-packages (from gradio==5.6.0) (0.46.1)\n", "Collecting tomlkit==0.12.0 (from gradio==5.6.0)\n", "  Using cached tomlkit-0.12.0-py3-none-any.whl (37 kB)\n", "Requirement already satisfied, skipping upgrade: typer<1.0,>=0.12; sys_platform != \"emscripten\" in /opt/conda/lib/python3.12/site-packages (from gradio==5.6.0) (0.15.2)\n", "Requirement already satisfied, skipping upgrade: uvicorn>=0.14.0; sys_platform != \"emscripten\" in /opt/conda/lib/python3.12/site-packages (from gradio==5.6.0) (0.34.0)\n", "Requirement already satisfied, skipping upgrade: dataclasses-json<0.7,>=0.5.7 in /opt/conda/lib/python3.12/site-packages (from langchain-community) (0.6.7)\n", "Requirement already satisfied, skipping upgrade: pydantic-settings<3.0.0,>=2.4.0 in /opt/conda/lib/python3.12/site-packages (from langchain-community) (2.8.1)\n", "Requirement already satisfied, skipping upgrade: httpx-sse<1.0.0,>=0.4.0 in /opt/conda/lib/python3.12/site-packages (from langchain-community) (0.4.0)\n", "Requirement already satisfied, skipping upgrade: pyarrow<21.0.0,>=18.0.0; sys_platform != \"darwin\" or platform_machine != \"x86_64\" in /opt/conda/lib/python3.12/site-packages (from awswrangler) (19.0.1)\n", "Requirement already satisfied, skipping upgrade: setuptools; python_version >= \"3.12\" in /opt/conda/lib/python3.12/site-packages (from awswrangler) (75.8.2)\n", "Collecting aiosqlite>=0.20 (from langgraph-checkpoint-sqlite)\n", "  Using cached aiosqlite-0.21.0-py3-none-any.whl (15 kB)\n", "Collecting sqlite-vec>=0.1.6 (from langgraph-checkpoint-sqlite)\n", "  Downloading sqlite_vec-0.1.6-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux1_x86_64.whl (151 kB)\n", "Requirement already satisfied, skipping upgrade: jsonpatch<2.0,>=1.33 in /opt/conda/lib/python3.12/site-packages (from langchain-core<1.0.0,>=0.3.58->langchain) (1.33)\n", "Requirement already satisfied, skipping upgrade: requests-toolbelt<2.0.0,>=1.0.0 in /opt/conda/lib/python3.12/site-packages (from langsmith<0.4,>=0.1.17->langchain) (1.0.0)\n", "Requirement already satisfied, skipping upgrade: annotated-types>=0.6.0 in /opt/conda/lib/python3.12/site-packages (from pydantic<3.0.0,>=2.7.4->langchain) (0.7.0)\n", "Requirement already satisfied, skipping upgrade: pydantic-core==2.23.4 in /opt/conda/lib/python3.12/site-packages (from pydantic<3.0.0,>=2.7.4->langchain) (2.23.4)\n", "Requirement already satisfied, skipping upgrade: charset_normalizer<4,>=2 in /opt/conda/lib/python3.12/site-packages (from requests<3,>=2->langchain) (3.4.1)\n", "Requirement already satisfied, skipping upgrade: idna<4,>=2.5 in /opt/conda/lib/python3.12/site-packages (from requests<3,>=2->langchain) (3.10)\n", "Requirement already satisfied, skipping upgrade: urllib3<3,>=1.21.1 in /opt/conda/lib/python3.12/site-packages (from requests<3,>=2->langchain) (2.3.0)\n", "Requirement already satisfied, skipping upgrade: certifi>=2017.4.17 in /opt/conda/lib/python3.12/site-packages (from requests<3,>=2->langchain) (2025.1.31)\n", "Requirement already satisfied, skipping upgrade: six>=1.5 in /opt/conda/lib/python3.12/site-packages (from python-dateutil->pyathena) (1.17.0)\n", "Requirement already satisfied, skipping upgrade: aioitertools<1.0.0,>=0.5.1 in /opt/conda/lib/python3.12/site-packages (from aiobotocore<3.0.0,>=2.5.4->s3fs) (0.12.0)\n", "Requirement already satisfied, skipping upgrade: multidict<7.0.0,>=6.0.0 in /opt/conda/lib/python3.12/site-packages (from aiobotocore<3.0.0,>=2.5.4->s3fs) (6.2.0)\n", "Requirement already satisfied, skipping upgrade: wrapt<2.0.0,>=1.10.10 in /opt/conda/lib/python3.12/site-packages (from aiobotocore<3.0.0,>=2.5.4->s3fs) (1.17.2)\n", "Requirement already satisfied, skipping upgrade: aiosignal>=1.1.2 in /opt/conda/lib/python3.12/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->s3fs) (1.3.2)\n", "Requirement already satisfied, skipping upgrade: attrs>=17.3.0 in /opt/conda/lib/python3.12/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->s3fs) (23.2.0)\n", "Requirement already satisfied, skipping upgrade: frozenlist>=1.1.1 in /opt/conda/lib/python3.12/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->s3fs) (1.5.0)\n", "Requirement already satisfied, skipping upgrade: yarl<2.0,>=1.0 in /opt/conda/lib/python3.12/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->s3fs) (1.18.3)\n", "Collecting ormsgpack<2.0.0,>=1.8.0 (from langgraph-checkpoint>=2.0.26->langgraph)\n", "  Downloading ormsgpack-1.10.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (216 kB)\n", "Requirement already satisfied, skipping upgrade: async-lru>=1.0.0 in /opt/conda/lib/python3.12/site-packages (from jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (2.0.5)\n", "Requirement already satisfied, skipping upgrade: ipykernel>=6.5.0 in /opt/conda/lib/python3.12/site-packages (from jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (6.29.5)\n", "Requirement already satisfied, skipping upgrade: jupyter-core in /opt/conda/lib/python3.12/site-packages (from jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (5.7.2)\n", "Requirement already satisfied, skipping upgrade: jupyter-lsp>=2.0.0 in /opt/conda/lib/python3.12/site-packages (from jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (2.2.5)\n", "Requirement already satisfied, skipping upgrade: jupyter-server<3,>=2.4.0 in /opt/conda/lib/python3.12/site-packages (from jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (2.15.0)\n", "Requirement already satisfied, skipping upgrade: jupyterlab-server<3,>=2.27.1 in /opt/conda/lib/python3.12/site-packages (from jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (2.27.3)\n", "Requirement already satisfied, skipping upgrade: notebook-shim>=0.2 in /opt/conda/lib/python3.12/site-packages (from jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (0.2.4)\n", "Requirement already satisfied, skipping upgrade: tornado>=6.2.0 in /opt/conda/lib/python3.12/site-packages (from jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (6.4.2)\n", "Requirement already satisfied, skipping upgrade: traitlets in /opt/conda/lib/python3.12/site-packages (from jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (5.14.3)\n", "Requirement already satisfied, skipping upgrade: sniffio>=1.1 in /opt/conda/lib/python3.12/site-packages (from anyio<5.0,>=3.0->gradio==5.6.0) (1.3.1)\n", "Collecting websockets<13.0,>=10.0 (from gradio-client==1.4.3->gradio==5.6.0)\n", "  Using cached websockets-12.0-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (131 kB)\n", "Requirement already satisfied, skipping upgrade: httpcore==1.* in /opt/conda/lib/python3.12/site-packages (from httpx>=0.24.1->gradio==5.6.0) (1.0.7)\n", "Requirement already satisfied, skipping upgrade: filelock in /opt/conda/lib/python3.12/site-packages (from huggingface-hub>=0.25.1->gradio==5.6.0) (3.18.0)\n", "Requirement already satisfied, skipping upgrade: tqdm>=4.42.1 in /opt/conda/lib/python3.12/site-packages (from huggingface-hub>=0.25.1->gradio==5.6.0) (4.67.1)\n", "Requirement already satisfied, skipping upgrade: pytz>=2020.1 in /opt/conda/lib/python3.12/site-packages (from pandas<3.0,>=1.0->gradio==5.6.0) (2024.1)\n", "Requirement already satisfied, skipping upgrade: tzdata>=2022.7 in /opt/conda/lib/python3.12/site-packages (from pandas<3.0,>=1.0->gradio==5.6.0) (2025.1)\n", "Requirement already satisfied, skipping upgrade: click>=8.0.0 in /opt/conda/lib/python3.12/site-packages (from typer<1.0,>=0.12; sys_platform != \"emscripten\"->gradio==5.6.0) (8.1.8)\n", "Requirement already satisfied, skipping upgrade: shellingham>=1.3.0 in /opt/conda/lib/python3.12/site-packages (from typer<1.0,>=0.12; sys_platform != \"emscripten\"->gradio==5.6.0) (1.5.4)\n", "Requirement already satisfied, skipping upgrade: rich>=10.11.0 in /opt/conda/lib/python3.12/site-packages (from typer<1.0,>=0.12; sys_platform != \"emscripten\"->gradio==5.6.0) (13.9.4)\n", "Requirement already satisfied, skipping upgrade: h11>=0.8 in /opt/conda/lib/python3.12/site-packages (from uvicorn>=0.14.0; sys_platform != \"emscripten\"->gradio==5.6.0) (0.14.0)\n", "Requirement already satisfied, skipping upgrade: marshmallow<4.0.0,>=3.18.0 in /opt/conda/lib/python3.12/site-packages (from dataclasses-json<0.7,>=0.5.7->langchain-community) (3.26.1)\n", "Requirement already satisfied, skipping upgrade: typing-inspect<1,>=0.4.0 in /opt/conda/lib/python3.12/site-packages (from dataclasses-json<0.7,>=0.5.7->langchain-community) (0.9.0)\n", "Requirement already satisfied, skipping upgrade: python-dotenv>=0.21.0 in /opt/conda/lib/python3.12/site-packages (from pydantic-settings<3.0.0,>=2.4.0->langchain-community) (1.0.1)\n", "Requirement already satisfied, skipping upgrade: jsonpointer>=1.9 in /opt/conda/lib/python3.12/site-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.58->langchain) (3.0.0)\n", "Requirement already satisfied, skipping upgrade: propcache>=0.2.0 in /opt/conda/lib/python3.12/site-packages (from yarl<2.0,>=1.0->aiohttp!=4.0.0a0,!=4.0.0a1->s3fs) (0.2.1)\n", "Requirement already satisfied, skipping upgrade: comm>=0.1.1 in /opt/conda/lib/python3.12/site-packages (from ipykernel>=6.5.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (0.2.2)\n", "Requirement already satisfied, skipping upgrade: debugpy>=1.6.5 in /opt/conda/lib/python3.12/site-packages (from ipykernel>=6.5.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (1.8.13)\n", "Requirement already satisfied, skipping upgrade: ipython>=7.23.1 in /opt/conda/lib/python3.12/site-packages (from ipykernel>=6.5.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (8.34.0)\n", "Requirement already satisfied, skipping upgrade: jupyter-client>=6.1.12 in /opt/conda/lib/python3.12/site-packages (from ipykernel>=6.5.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (8.6.3)\n", "Requirement already satisfied, skipping upgrade: matplotlib-inline>=0.1 in /opt/conda/lib/python3.12/site-packages (from ipykernel>=6.5.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (0.1.7)\n", "Requirement already satisfied, skipping upgrade: nest-asyncio in /opt/conda/lib/python3.12/site-packages (from ipykernel>=6.5.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (1.6.0)\n", "Requirement already satisfied, skipping upgrade: psutil in /opt/conda/lib/python3.12/site-packages (from ipykernel>=6.5.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (5.9.8)\n", "Requirement already satisfied, skipping upgrade: pyzmq>=24 in /opt/conda/lib/python3.12/site-packages (from ipykernel>=6.5.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (26.3.0)\n", "Requirement already satisfied, skipping upgrade: platformdirs>=2.5 in /opt/conda/lib/python3.12/site-packages (from jupyter-core->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (4.3.6)\n", "Requirement already satisfied, skipping upgrade: argon2-cffi>=21.1 in /opt/conda/lib/python3.12/site-packages (from jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (23.1.0)\n", "Requirement already satisfied, skipping upgrade: jupyter-events>=0.11.0 in /opt/conda/lib/python3.12/site-packages (from jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (0.12.0)\n", "Requirement already satisfied, skipping upgrade: jupyter-server-terminals>=0.4.4 in /opt/conda/lib/python3.12/site-packages (from jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (0.5.3)\n", "Requirement already satisfied, skipping upgrade: nbconvert>=6.4.4 in /opt/conda/lib/python3.12/site-packages (from jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (7.16.6)\n", "Requirement already satisfied, skipping upgrade: nbformat>=5.3.0 in /opt/conda/lib/python3.12/site-packages (from jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (5.10.4)\n", "Requirement already satisfied, skipping upgrade: overrides>=5.0 in /opt/conda/lib/python3.12/site-packages (from jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (7.7.0)\n", "Requirement already satisfied, skipping upgrade: prometheus-client>=0.9 in /opt/conda/lib/python3.12/site-packages (from jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (0.21.1)\n", "Requirement already satisfied, skipping upgrade: send2trash>=1.8.2 in /opt/conda/lib/python3.12/site-packages (from jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (1.8.3)\n", "Requirement already satisfied, skipping upgrade: terminado>=0.8.3 in /opt/conda/lib/python3.12/site-packages (from jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (0.18.1)\n", "Requirement already satisfied, skipping upgrade: websocket-client>=1.7 in /opt/conda/lib/python3.12/site-packages (from jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (1.8.0)\n", "Requirement already satisfied, skipping upgrade: babel>=2.10 in /opt/conda/lib/python3.12/site-packages (from jupyterlab-server<3,>=2.27.1->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (2.17.0)\n", "Requirement already satisfied, skipping upgrade: json5>=0.9.0 in /opt/conda/lib/python3.12/site-packages (from jupyterlab-server<3,>=2.27.1->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (0.10.0)\n", "Requirement already satisfied, skipping upgrade: jsonschema>=4.18.0 in /opt/conda/lib/python3.12/site-packages (from jupyterlab-server<3,>=2.27.1->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (4.23.0)\n", "Requirement already satisfied, skipping upgrade: markdown-it-py>=2.2.0 in /opt/conda/lib/python3.12/site-packages (from rich>=10.11.0->typer<1.0,>=0.12; sys_platform != \"emscripten\"->gradio==5.6.0) (3.0.0)\n", "Requirement already satisfied, skipping upgrade: pygments<3.0.0,>=2.13.0 in /opt/conda/lib/python3.12/site-packages (from rich>=10.11.0->typer<1.0,>=0.12; sys_platform != \"emscripten\"->gradio==5.6.0) (2.19.1)\n", "Requirement already satisfied, skipping upgrade: mypy_extensions>=0.3.0 in /opt/conda/lib/python3.12/site-packages (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain-community) (1.0.0)\n", "Requirement already satisfied, skipping upgrade: decorator in /opt/conda/lib/python3.12/site-packages (from ipython>=7.23.1->ipykernel>=6.5.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (5.2.1)\n", "Requirement already satisfied, skipping upgrade: jedi>=0.16 in /opt/conda/lib/python3.12/site-packages (from ipython>=7.23.1->ipykernel>=6.5.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (0.19.2)\n", "Requirement already satisfied, skipping upgrade: pexpect>4.3; sys_platform != \"win32\" and sys_platform != \"emscripten\" in /opt/conda/lib/python3.12/site-packages (from ipython>=7.23.1->ipykernel>=6.5.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (4.9.0)\n", "Requirement already satisfied, skipping upgrade: prompt_toolkit<3.1.0,>=3.0.41 in /opt/conda/lib/python3.12/site-packages (from ipython>=7.23.1->ipykernel>=6.5.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (3.0.50)\n", "Requirement already satisfied, skipping upgrade: stack_data in /opt/conda/lib/python3.12/site-packages (from ipython>=7.23.1->ipykernel>=6.5.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (0.6.3)\n", "Requirement already satisfied, skipping upgrade: argon2-cffi-bindings in /opt/conda/lib/python3.12/site-packages (from argon2-cffi>=21.1->jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (21.2.0)\n", "Requirement already satisfied, skipping upgrade: python-json-logger>=2.0.4 in /opt/conda/lib/python3.12/site-packages (from jupyter-events>=0.11.0->jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (2.0.7)\n", "Requirement already satisfied, skipping upgrade: referencing in /opt/conda/lib/python3.12/site-packages (from jupyter-events>=0.11.0->jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (0.36.2)\n", "Requirement already satisfied, skipping upgrade: rfc3339-validator in /opt/conda/lib/python3.12/site-packages (from jupyter-events>=0.11.0->jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (0.1.4)\n", "Requirement already satisfied, skipping upgrade: rfc3986-validator>=0.1.1 in /opt/conda/lib/python3.12/site-packages (from jupyter-events>=0.11.0->jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (0.1.1)\n", "Requirement already satisfied, skipping upgrade: beautifulsoup4 in /opt/conda/lib/python3.12/site-packages (from nbconvert>=6.4.4->jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (4.13.3)\n", "Requirement already satisfied, skipping upgrade: bleach[css]!=5.0.0 in /opt/conda/lib/python3.12/site-packages (from nbconvert>=6.4.4->jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (6.2.0)\n", "Requirement already satisfied, skipping upgrade: defusedxml in /opt/conda/lib/python3.12/site-packages (from nbconvert>=6.4.4->jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (0.7.1)\n", "Requirement already satisfied, skipping upgrade: jupyterlab-pygments in /opt/conda/lib/python3.12/site-packages (from nbconvert>=6.4.4->jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (0.3.0)\n", "Requirement already satisfied, skipping upgrade: mistune<4,>=2.0.3 in /opt/conda/lib/python3.12/site-packages (from nbconvert>=6.4.4->jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (3.1.3)\n", "Requirement already satisfied, skipping upgrade: nbclient>=0.5.0 in /opt/conda/lib/python3.12/site-packages (from nbconvert>=6.4.4->jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (0.10.2)\n", "Requirement already satisfied, skipping upgrade: pandocfilters>=1.4.1 in /opt/conda/lib/python3.12/site-packages (from nbconvert>=6.4.4->jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (1.5.0)\n", "Requirement already satisfied, skipping upgrade: fastjsonschema>=2.15 in /opt/conda/lib/python3.12/site-packages (from nbformat>=5.3.0->jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (2.21.1)\n", "Requirement already satisfied, skipping upgrade: ptyprocess; os_name != \"nt\" in /opt/conda/lib/python3.12/site-packages (from terminado>=0.8.3->jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (0.7.0)\n", "Requirement already satisfied, skipping upgrade: jsonschema-specifications>=2023.03.6 in /opt/conda/lib/python3.12/site-packages (from jsonschema>=4.18.0->jupyterlab-server<3,>=2.27.1->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (2024.10.1)\n", "Requirement already satisfied, skipping upgrade: rpds-py>=0.7.1 in /opt/conda/lib/python3.12/site-packages (from jsonschema>=4.18.0->jupyterlab-server<3,>=2.27.1->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (0.23.1)\n", "Requirement already satisfied, skipping upgrade: mdurl~=0.1 in /opt/conda/lib/python3.12/site-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->typer<1.0,>=0.12; sys_platform != \"emscripten\"->gradio==5.6.0) (0.1.2)\n", "Requirement already satisfied, skipping upgrade: parso<0.9.0,>=0.8.4 in /opt/conda/lib/python3.12/site-packages (from jedi>=0.16->ipython>=7.23.1->ipykernel>=6.5.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (0.8.4)\n", "Requirement already satisfied, skipping upgrade: wcwidth in /opt/conda/lib/python3.12/site-packages (from prompt_toolkit<3.1.0,>=3.0.41->ipython>=7.23.1->ipykernel>=6.5.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (0.2.13)\n", "Requirement already satisfied, skipping upgrade: executing>=1.2.0 in /opt/conda/lib/python3.12/site-packages (from stack_data->ipython>=7.23.1->ipykernel>=6.5.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (2.1.0)\n", "Requirement already satisfied, skipping upgrade: asttokens>=2.1.0 in /opt/conda/lib/python3.12/site-packages (from stack_data->ipython>=7.23.1->ipykernel>=6.5.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (3.0.0)\n", "Requirement already satisfied, skipping upgrade: pure_eval in /opt/conda/lib/python3.12/site-packages (from stack_data->ipython>=7.23.1->ipykernel>=6.5.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (0.2.3)\n", "Requirement already satisfied, skipping upgrade: cffi>=1.0.1 in /opt/conda/lib/python3.12/site-packages (from argon2-cffi-bindings->argon2-cffi>=21.1->jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (1.17.1)\n", "Requirement already satisfied, skipping upgrade: soupsieve>1.2 in /opt/conda/lib/python3.12/site-packages (from beautifulsoup4->nbconvert>=6.4.4->jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (2.5)\n", "Requirement already satisfied, skipping upgrade: webencodings in /opt/conda/lib/python3.12/site-packages (from bleach[css]!=5.0.0->nbconvert>=6.4.4->jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (0.5.1)\n", "Requirement already satisfied, skipping upgrade: tinycss2<1.5,>=1.1.0; extra == \"css\" in /opt/conda/lib/python3.12/site-packages (from bleach[css]!=5.0.0->nbconvert>=6.4.4->jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (1.4.0)\n", "Requirement already satisfied, skipping upgrade: pycparser in /opt/conda/lib/python3.12/site-packages (from cffi>=1.0.1->argon2-cffi-bindings->argon2-cffi>=21.1->jupyter-server<3,>=2.4.0->jupyterlab<5.0,>=4.0->amazon-codewhisperer-jupyterlab-ext) (2.22)\n", "Installing collected packages: langchain-core, langchain-text-splitters, SQLAlchemy, langchain, s3transfer, boto3, pyathena, s3fs, ormsgpack, langgraph-checkpoint, langgraph-prebuilt, langgraph-sdk, langgraph, amazon-codewhisperer-jupyterlab-ext, aiofiles, ffmpy, websockets, gradio-client, markupsafe, pydub, python-multipart, ruff, safehttpx, semantic-version, tomlkit, gradio, langchain-community, awswrangler, langchain_experimental, langchain_aws, aiosqlite, sqlite-vec, langgraph-checkpoint-sqlite\n", "  Attempting uninstall: langchain-core\n", "    Found existing installation: langchain-core 0.3.46\n", "    Uninstalling langchain-core-0.3.46:\n", "      Successfully uninstalled langchain-core-0.3.46\n", "  Attempting uninstall: langchain-text-splitters\n", "    Found existing installation: langchain-text-splitters 0.3.7\n", "    Uninstalling langchain-text-splitters-0.3.7:\n", "      Successfully uninstalled langchain-text-splitters-0.3.7\n", "  Attempting uninstall: SQLAlchemy\n", "    Found existing installation: SQLAlchemy 2.0.39\n", "    Uninstalling SQLAlchemy-2.0.39:\n", "      Successfully uninstalled SQLAlchemy-2.0.39\n", "  Attempting uninstall: langchain\n", "    Found existing installation: langchain 0.3.21\n", "    Uninstalling langchain-0.3.21:\n", "      Successfully uninstalled langchain-0.3.21\n", "  Attempting uninstall: s3transfer\n", "    Found existing installation: s3transfer 0.11.3\n", "    Uninstalling s3transfer-0.11.3:\n", "      Successfully uninstalled s3transfer-0.11.3\n", "  Attempting uninstall: boto3\n", "    Found existing installation: boto3 1.37.1\n", "    Uninstalling boto3-1.37.1:\n", "      Successfully uninstalled boto3-1.37.1\n", "  Attempting uninstall: pya<PERSON><PERSON>\n", "    Found existing installation: PyAthena 3.12.2\n", "    Uninstalling PyAthena-3.12.2:\n", "      Successfully uninstalled PyAthena-3.12.2\n", "  Attempting uninstall: s3fs\n", "    Found existing installation: s3fs 2024.10.0\n", "    Uninstalling s3fs-2024.10.0:\n", "      Successfully uninstalled s3fs-2024.10.0\n", "  Attempting uninstall: aiofiles\n", "    Found existing installation: aiofiles 24.1.0\n", "    Uninstalling aiofiles-24.1.0:\n", "      Successfully uninstalled aiofiles-24.1.0\n", "  Attempting uninstall: websockets\n", "    Found existing installation: websockets 15.0.1\n", "    Uninstalling websockets-15.0.1:\n", "      Successfully uninstalled websockets-15.0.1\n", "  Attempting uninstall: markups<PERSON>e\n", "    Found existing installation: MarkupSafe 3.0.2\n", "    Uninstalling MarkupSafe-3.0.2:\n", "      Successfully uninstalled MarkupSafe-3.0.2\n", "  Attempting uninstall: python-multipart\n", "    Found existing installation: python-multipart 0.0.20\n", "    Uninstalling python-multipart-0.0.20:\n", "      Successfully uninstalled python-multipart-0.0.20\n", "  Attempting uninstall: to<PERSON><PERSON><PERSON>\n", "    Found existing installation: tomlkit 0.13.2\n", "    Uninstalling tomlkit-0.13.2:\n", "      Successfully uninstalled tomlkit-0.13.2\n", "  Attempting uninstall: langchain-community\n", "    Found existing installation: langchain-community 0.3.20\n", "    Uninstalling langchain-community-0.3.20:\n", "      Successfully uninstalled langchain-community-0.3.20\n", "  Attempting uninstall: langchain_aws\n", "    Found existing installation: langchain-aws 0.2.10\n", "    Uninstalling langchain-aws-0.2.10:\n", "      Successfully uninstalled langchain-aws-0.2.10\n", "  Attempting uninstall: aiosqlite\n", "    Found existing installation: aiosqlite 0.19.0\n", "    Uninstalling aiosqlite-0.19.0:\n", "      Successfully uninstalled aiosqlite-0.19.0\n", "\u001b[31mERROR: pip's legacy dependency resolver does not consider dependency conflicts when selecting packages. This behaviour is the source of the following dependency conflicts.\n", "autogluon-multimodal 1.2 requires nvidia-ml-py3==7.352.0, which is not installed.\n", "amazon-sagemaker-sql-magic 0.1.3 requires sqlparse==0.5.0, but you'll have sqlparse 0.5.3 which is incompatible.\n", "autogluon-multimodal 1.2 requires jsonschema<4.22,>=4.18, but you'll have jsonschema 4.23.0 which is incompatible.\n", "autogluon-multimodal 1.2 requires nltk<3.9,>=3.4.5, but you'll have nltk 3.9.1 which is incompatible.\n", "autogluon-multimodal 1.2 requires omegaconf<2.3.0,>=2.1.1, but you'll have omegaconf 2.3.0 which is incompatible.\n", "boto3 1.38.36 requires botocore<1.39.0,>=1.38.36, but you'll have botocore 1.37.1 which is incompatible.\n", "jupyter-ai-magics 2.30.0 requires pydantic<3,>=2.10.0, but you'll have pydantic 2.9.2 which is incompatible.\n", "langchain-aws 0.2.25 requires pydantic<3,>=2.10.0, but you'll have pydantic 2.9.2 which is incompatible.\n", "langchain-core 0.3.65 requires langsmith<0.4,>=0.3.45, but you'll have langsmith 0.2.11 which is incompatible.\n", "s3fs 2025.5.1 requires fsspec==2025.5.1, but you'll have fsspec 2024.10.0 which is incompatible.\n", "s3transfer 0.13.0 requires botocore<2.0a.0,>=1.37.4, but you'll have botocore 1.37.1 which is incompatible.\n", "sparkmagic 0.21.0 requires pandas<2.0.0,>=0.17.1, but you'll have pandas 2.2.3 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed SQLAlchemy-2.0.41 aiofiles-23.2.1 aiosqlite-0.21.0 amazon-codewhisperer-jupyterlab-ext-2.0.2 awswrangler-3.12.0 boto3-1.38.36 ffmpy-0.6.0 gradio-5.6.0 gradio-client-1.4.3 langchain-0.3.25 langchain-community-0.3.25 langchain-core-0.3.65 langchain-text-splitters-0.3.8 langchain_aws-0.2.25 langchain_experimental-0.3.4 langgraph-0.4.8 langgraph-checkpoint-2.0.26 langgraph-checkpoint-sqlite-2.0.10 langgraph-prebuilt-0.2.2 langgraph-sdk-0.1.70 markupsafe-2.1.5 ormsgpack-1.10.0 pyathena-3.14.1 pydub-0.25.1 python-multipart-0.0.12 ruff-0.11.13 s3fs-2025.5.1 s3transfer-0.13.0 safehttpx-0.1.6 semantic-version-2.10.0 sqlite-vec-0.1.6 tomlkit-0.12.0 websockets-12.0\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install -U langchain SQLAlchemy pyathena boto3 s3fs langgraph amazon-codewhisperer-jupyterlab-ext gradio==5.6.0 langchain-community awswrangler langchain_experimental langchain_aws langgraph-checkpoint-sqlite --use-deprecated=legacy-resolver"]}, {"cell_type": "code", "execution_count": 3, "id": "c2907cd7-1cc3-49bd-ab09-843bf3b2cfff", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: pip in /opt/conda/lib/python3.12/site-packages (25.0.1)\n", "Collecting pip\n", "  Using cached pip-25.1.1-py3-none-any.whl.metadata (3.6 kB)\n", "Using cached pip-25.1.1-py3-none-any.whl (1.8 MB)\n", "Installing collected packages: pip\n", "  Attempting uninstall: pip\n", "    Found existing installation: pip 25.0.1\n", "    Uninstalling pip-25.0.1:\n", "      Successfully uninstalled pip-25.0.1\n", "Successfully installed pip-25.1.1\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install --upgrade pip"]}, {"cell_type": "code", "execution_count": 4, "id": "26442f5c-fcbf-4826-aeec-147849f85288", "metadata": {"tags": []}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings('ignore')"]}, {"cell_type": "code", "execution_count": 5, "id": "002b9e8c-85c1-446a-93b1-4df058945965", "metadata": {"tags": []}, "outputs": [], "source": ["from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "from langchain.chains.summarize import load_summarize_chain\n", "from langchain_community.llms.bedrock import Bedrock\n", "from langchain.llms import OpenAI\n", "from langchain.chat_models import ChatOpenAI\n", "from langchain.docstore.document import Document\n", "from langchain.embeddings import BedrockEmbeddings\n", "from langchain.chains import RetrievalQA\n", "from langchain.vectorstores import FAISS\n", "from langchain.chains import create_sql_query_chain\n", "from langchain.llms.bedrock import Bedrock\n", "from langchain_experimental.sql import SQLDatabaseChain\n", "from langchain.utilities.sql_database import SQLDatabase\n", "from sqlalchemy import create_engine\n", "from langchain_community.document_loaders.athena import AthenaLoader\n", "from langchain.chains import create_sql_query_chain\n", "from langchain_community.tools.sql_database.tool import QuerySQLDataBaseTool\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.prompts import ChatPromptTemplate\n", "#from langchain_community.agent_toolkits import SQLDatabaseToolkit\n", "from langchain_core.messages import HumanMessage,AIMessage,SystemMessage\n", "from langgraph.prebuilt import create_react_agent\n", "from langchain_core.output_parsers import StrOutputParser\n", "import gradio as gr\n", "import random\n", "import string"]}, {"cell_type": "code", "execution_count": 6, "id": "7590ca38-28f0-40cb-92c9-07acee20195f", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dockerfile                            output.txt\n", "Untitled.ipynb                        output_final.txt\n", "Untitled1.ipynb                       requirements.txt\n", "chat_with_sql.ipynb                   run_bash.sh\n", "chat_with_sql.py                      temp_query.txt\n", "chat_with_sql_list.ipynb              \u001b[0m\u001b[01;34mutils\u001b[0m/\n", "chat_with_sql_more_tables.ipynb       veeLink_events.ipynb\n", "chat_with_sql_more_tables.py          veeLink_events_new_table.ipynb\n", "chat_with_sql_more_tables_list.ipynb  \u001b[01;34mveeva_link_pipieline\u001b[0m/\n", "\u001b[01;34mlc_community\u001b[0m/\n"]}], "source": ["ls"]}, {"cell_type": "code", "execution_count": 7, "id": "803e34b8-fa6f-4382-aac0-6945d1c128a4", "metadata": {"tags": []}, "outputs": [], "source": ["from lc_community.agent_toolkits.new_toolkit import SQLDatabaseToolkit"]}, {"cell_type": "code", "execution_count": 8, "id": "dd03c226-757d-4f60-943a-39788d1ef164", "metadata": {"tags": []}, "outputs": [], "source": ["from urllib.parse import quote_plus\n", "from sqlalchemy import create_engine\n", "from langchain_community.utilities import SQLDatabase\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.runnables import RunnablePassthrough\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_community.chat_models import BedrockChat\n", "#from langchain_openai import ChatOpenAI\n", "from langchain_core.messages import SystemMessage\n", "from langchain_aws.chat_models.bedrock import ChatBedrock\n", "from langchain_core.prompts import PromptTemplate\n", "from langgraph.checkpoint.sqlite import SqliteSaver\n", "from langgraph.checkpoint.memory import MemorySaver"]}, {"cell_type": "code", "execution_count": 9, "id": "809dd381-0a06-47da-87ec-3b03b7a288c3", "metadata": {"tags": []}, "outputs": [], "source": ["# pip install --upgrade boto3 botocore"]}, {"cell_type": "code", "execution_count": 10, "id": "9d118580-ec20-4b69-aa46-afa47d1dd435", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting boto3==1.34.0\n", "  Using cached boto3-1.34.0-py3-none-any.whl.metadata (6.6 kB)\n", "Collecting botocore==1.34.0\n", "  Using cached botocore-1.34.0-py3-none-any.whl.metadata (5.6 kB)\n", "Requirement already satisfied: jmespath<2.0.0,>=0.7.1 in /opt/conda/lib/python3.12/site-packages (from boto3==1.34.0) (1.0.1)\n", "Collecting s3transfer<0.10.0,>=0.9.0 (from boto3==1.34.0)\n", "  Using cached s3transfer-0.9.0-py3-none-any.whl.metadata (1.7 kB)\n", "Requirement already satisfied: python-dateutil<3.0.0,>=2.1 in /opt/conda/lib/python3.12/site-packages (from botocore==1.34.0) (2.9.0.post0)\n", "Collecting urllib3<2.1,>=1.25.4 (from botocore==1.34.0)\n", "  Using cached urllib3-2.0.7-py3-none-any.whl.metadata (6.6 kB)\n", "Requirement already satisfied: six>=1.5 in /opt/conda/lib/python3.12/site-packages (from python-dateutil<3.0.0,>=2.1->botocore==1.34.0) (1.17.0)\n", "Using cached boto3-1.34.0-py3-none-any.whl (139 kB)\n", "Using cached botocore-1.34.0-py3-none-any.whl (11.8 MB)\n", "Using cached s3transfer-0.9.0-py3-none-any.whl (82 kB)\n", "Using cached urllib3-2.0.7-py3-none-any.whl (124 kB)\n", "Installing collected packages: urllib3, botocore, s3transfer, boto3\n", "\u001b[2K  Attempting uninstall: urllib3\n", "\u001b[2K    Found existing installation: urllib3 2.3.0\n", "\u001b[2K    Uninstalling urllib3-2.3.0:\n", "\u001b[2K      Successfully uninstalled urllib3-2.3.0\n", "\u001b[2K  Attempting uninstall: botocore\n", "\u001b[2K    Found existing installation: botocore 1.37.1\n", "\u001b[2K    Uninstalling botocore-1.37.1:[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1/4\u001b[0m [botocore]\n", "\u001b[2K      Successfully uninstalled botocore-1.37.1━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1/4\u001b[0m [botocore]\n", "\u001b[2K  Attempting uninstall: s3transfer90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1/4\u001b[0m [botocore]\n", "\u001b[2K    Found existing installation: s3transfer 0.13.0━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2/4\u001b[0m [s3transfer]\n", "\u001b[2K    Uninstalling s3transfer-0.13.0:[0m\u001b[90m━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2/4\u001b[0m [s3transfer]\n", "\u001b[2K      Successfully uninstalled s3transfer-0.13.0━━━━━━━━━━━━━━\u001b[0m \u001b[32m2/4\u001b[0m [s3transfer]\n", "\u001b[2K  Attempting uninstall: boto3[90m╺\u001b[0m\u001b[90m━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2/4\u001b[0m [s3transfer]\n", "\u001b[2K    Found existing installation: boto3 1.38.36━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2/4\u001b[0m [s3transfer]\n", "\u001b[2K    Uninstalling boto3-1.38.36:0m╺\u001b[0m\u001b[90m━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2/4\u001b[0m [s3transfer]\n", "\u001b[2K      Successfully uninstalled boto3-1.38.36━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2/4\u001b[0m [s3transfer]\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m4/4\u001b[0m [boto3]/4\u001b[0m [s3transfer]\n", "\u001b[1A\u001b[2K\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "autogluon-multimodal 1.2 requires nvidia-ml-py3==7.352.0, which is not installed.\n", "aiobotocore 2.21.1 requires botocore<1.37.2,>=1.37.0, but you have botocore 1.34.0 which is incompatible.\n", "amazon-sagemaker-sql-magic 0.1.3 requires sqlparse==0.5.0, but you have sqlparse 0.5.3 which is incompatible.\n", "autogluon-multimodal 1.2 requires jsonschema<4.22,>=4.18, but you have jsonschema 4.23.0 which is incompatible.\n", "autogluon-multimodal 1.2 requires nltk<3.9,>=3.4.5, but you have nltk 3.9.1 which is incompatible.\n", "autogluon-multimodal 1.2 requires omegaconf<2.3.0,>=2.1.1, but you have omegaconf 2.3.0 which is incompatible.\n", "sagemaker 2.242.0 requires boto3<2.0,>=1.35.75, but you have boto3 1.34.0 which is incompatible.\n", "sagemaker-core 1.0.25 requires boto3<2.0.0,>=1.35.75, but you have boto3 1.34.0 which is incompatible.\n", "sparkmagic 0.21.0 requires pandas<2.0.0,>=0.17.1, but you have pandas 2.2.3 which is incompatible.\n", "s3fs 2025.5.1 requires fsspec==2025.5.1, but you have fsspec 2024.10.0 which is incompatible.\n", "langchain-aws 0.2.25 requires boto3>=1.37.24, but you have boto3 1.34.0 which is incompatible.\n", "langchain-aws 0.2.25 requires pydantic<3,>=2.10.0, but you have pydantic 2.9.2 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed boto3-1.34.0 botocore-1.34.0 s3transfer-0.9.0 urllib3-2.0.7\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install boto3==1.34.0 botocore==1.34.0"]}, {"cell_type": "code", "execution_count": 11, "id": "96eeac18-68fa-4139-ad08-2ee2448f2918", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Name: boto3\n", "Version: 1.34.0\n", "Summary: The AWS SDK for Python\n", "Home-page: https://github.com/boto/boto3\n", "Author: Amazon Web Services\n", "Author-email: \n", "License: Apache License 2.0\n", "Location: /opt/conda/lib/python3.12/site-packages\n", "Requires: botocore, jmespath, s3transfer\n", "Required-by: amazon-codewhisperer-jupyterlab-ext, amazon-q-developer-jupyterlab-ext, amazon-sagemaker-sql-editor, amazon-sagemaker-sql-execution, amazon_sagemaker_jupyter_ai_q_developer, autogluon.common, autogluon.core, autogluon.multimodal, aws-glue-sessions, awswrangler, langchain-aws, PyAthena, redshift_connector, sagemaker, sagemaker-core, sagemaker-kernel-wrapper, sagemaker-mlflow, sagemaker-studio-analytics-extension, sagemaker-studio-sparkmagic-lib\n", "---\n", "Name: botocore\n", "Version: 1.34.0\n", "Summary: Low-level, data-driven core of boto 3.\n", "Home-page: https://github.com/boto/botocore\n", "Author: Amazon Web Services\n", "Author-email: \n", "License: Apache License 2.0\n", "Location: /opt/conda/lib/python3.12/site-packages\n", "Requires: j<PERSON><PERSON>, python-dateutil, urllib3\n", "Required-by: a<PERSON><PERSON>ocore, amazon-codewhisperer-jupyterlab-ext, amazon-q-developer-jupyterlab-ext, amazon-sagemaker-sql-editor, amazon_sagemaker_jupyter_ai_q_developer, aws-glue-sessions, awswrangler, boto3, git-remote-codecommit, PyAthena, redshift_connector, s3transfer\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip show boto3 botocore"]}, {"cell_type": "code", "execution_count": 12, "id": "257afaf9-45e7-43c2-bedb-82938a28df1e", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['ArnParser', 'CloudFrontSigner', 'OrderedDict', 'ParamValidationError', 'RequestSigner', 'S3PostPresigner', 'UnknownClientMethodError', 'UnknownSignatureVersionError', 'UnsupportedSignatureVersionError', '__builtins__', '__cached__', '__doc__', '__file__', '__loader__', '__name__', '__package__', '__spec__', '_dsql_generate_db_auth_token', '_should_use_global_endpoint', 'add_dsql_generate_db_auth_token_methods', 'add_generate_db_auth_token', 'add_generate_presigned_post', 'add_generate_presigned_url', 'base64', 'botocore', 'create_request_object', 'datetime', 'datetime2timestamp', 'dsql_generate_db_connect_admin_auth_token', 'dsql_generate_db_connect_auth_token', 'fix_s3_host', 'generate_db_auth_token', 'generate_presigned_post', 'generate_presigned_url', 'json', 'prepare_request_dict', 'weakref']\n"]}], "source": ["import botocore.signers\n", "print(dir(botocore.signers))"]}, {"cell_type": "code", "execution_count": 13, "id": "5e0abe60-5ed0-4272-804c-3d1bd6cf07ed", "metadata": {"tags": []}, "outputs": [], "source": ["import json\n", "import os\n", "import pandas as pd\n", "import re\n", "import boto3\n", "import awswrangler as wr\n", "from pyathena import connect\n", "from io import StringIO\n", "import traceback\n", "import sys"]}, {"cell_type": "code", "execution_count": 14, "id": "05e885b7-eb46-496a-baea-5ba6ce6df6d6", "metadata": {"tags": []}, "outputs": [], "source": ["from langchain_core.output_parsers.openai_tools import PydanticToolsParser\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from pydantic import BaseModel, Field"]}, {"cell_type": "code", "execution_count": 15, "id": "7ddebbf3-f7df-42a7-9e29-11c279178c30", "metadata": {"tags": []}, "outputs": [], "source": ["#llm setup"]}, {"cell_type": "code", "execution_count": 16, "id": "c6ad9179-038f-4362-8fb2-888c82275b3e", "metadata": {"tags": []}, "outputs": [], "source": ["module_path = \"..\"\n", "sys.path.append(os.path.abspath(module_path))\n", "from utils import bedrock, print_ww"]}, {"cell_type": "code", "execution_count": 17, "id": "a3e0ebcc-5e63-46bd-a5e7-49269617e823", "metadata": {"tags": []}, "outputs": [], "source": ["# pip install --upgrade boto3 botocore"]}, {"cell_type": "code", "execution_count": 18, "id": "4b96f776-4761-482b-801a-2f26b118d2ba", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Create new client\n", "  Using region: us-west-2\n"]}, {"ename": "InvalidChecksumConfigError", "evalue": "Unsupported configuration value for request_checksum_calculation. Expected one of ('when_supported', 'when_required') but got None.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mInvalidChecksumConfigError\u001b[0m                Traceback (most recent call last)", "Cell \u001b[0;32mIn[18], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m boto3_bedrock \u001b[38;5;241m=\u001b[39m \u001b[43mbedrock\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_bedrock_client\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m      2\u001b[0m \u001b[43m    \u001b[49m\u001b[43massumed_role\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mos\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43menviron\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mBEDROCK_ASSUME_ROLE\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m      3\u001b[0m \u001b[43m    \u001b[49m\u001b[43mregion\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mos\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43menviron\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mAWS_DEFAULT_REGION\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m      4\u001b[0m \u001b[43m)\u001b[49m\n", "File \u001b[0;32m/mnt/custom-file-systems/efs/fs-09d18f54efda548e0_fsap-0d2d5aa2d40d94fed/React_LLM/ReAct_SQL_New/utils/bedrock.py:71\u001b[0m, in \u001b[0;36mget_bedrock_client\u001b[0;34m(assumed_role, region, runtime)\u001b[0m\n\u001b[1;32m     68\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m     69\u001b[0m     service_name\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mbedrock\u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m---> 71\u001b[0m bedrock_client \u001b[38;5;241m=\u001b[39m \u001b[43msession\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mclient\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     72\u001b[0m \u001b[43m    \u001b[49m\u001b[43mservice_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mservice_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     73\u001b[0m \u001b[43m    \u001b[49m\u001b[43mconfig\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mretry_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     74\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mclient_kwargs\u001b[49m\n\u001b[1;32m     75\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     77\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mboto3 Bedrock client successfully created!\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     78\u001b[0m \u001b[38;5;28mprint\u001b[39m(bedrock_client\u001b[38;5;241m.\u001b[39m_endpoint)\n", "File \u001b[0;32m/opt/conda/lib/python3.12/site-packages/boto3/session.py:299\u001b[0m, in \u001b[0;36mSession.client\u001b[0;34m(self, service_name, region_name, api_version, use_ssl, verify, endpoint_url, aws_access_key_id, aws_secret_access_key, aws_session_token, config)\u001b[0m\n\u001b[1;32m    217\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mclient\u001b[39m(\n\u001b[1;32m    218\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m    219\u001b[0m     service_name,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    228\u001b[0m     config\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m    229\u001b[0m ):\n\u001b[1;32m    230\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    231\u001b[0m \u001b[38;5;124;03m    Create a low-level service client by name.\u001b[39;00m\n\u001b[1;32m    232\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    297\u001b[0m \n\u001b[1;32m    298\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 299\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_session\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcreate_client\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    300\u001b[0m \u001b[43m        \u001b[49m\u001b[43mservice_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    301\u001b[0m \u001b[43m        \u001b[49m\u001b[43mregion_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mregion_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    302\u001b[0m \u001b[43m        \u001b[49m\u001b[43mapi_version\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mapi_version\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    303\u001b[0m \u001b[43m        \u001b[49m\u001b[43muse_ssl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43muse_ssl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    304\u001b[0m \u001b[43m        \u001b[49m\u001b[43mverify\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mverify\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    305\u001b[0m \u001b[43m        \u001b[49m\u001b[43mendpoint_url\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mendpoint_url\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    306\u001b[0m \u001b[43m        \u001b[49m\u001b[43maws_access_key_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maws_access_key_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    307\u001b[0m \u001b[43m        \u001b[49m\u001b[43maws_secret_access_key\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maws_secret_access_key\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    308\u001b[0m \u001b[43m        \u001b[49m\u001b[43maws_session_token\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maws_session_token\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    309\u001b[0m \u001b[43m        \u001b[49m\u001b[43mconfig\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    310\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.12/site-packages/botocore/session.py:997\u001b[0m, in \u001b[0;36mSession.create_client\u001b[0;34m(self, service_name, region_name, api_version, use_ssl, verify, endpoint_url, aws_access_key_id, aws_secret_access_key, aws_session_token, config)\u001b[0m\n\u001b[1;32m    980\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_add_configured_endpoint_provider(\n\u001b[1;32m    981\u001b[0m     client_name\u001b[38;5;241m=\u001b[39mservice_name,\n\u001b[1;32m    982\u001b[0m     config_store\u001b[38;5;241m=\u001b[39mconfig_store,\n\u001b[1;32m    983\u001b[0m )\n\u001b[1;32m    985\u001b[0m client_creator \u001b[38;5;241m=\u001b[39m botocore\u001b[38;5;241m.\u001b[39mclient\u001b[38;5;241m.\u001b[39mClientCreator(\n\u001b[1;32m    986\u001b[0m     loader,\n\u001b[1;32m    987\u001b[0m     endpoint_resolver,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    995\u001b[0m     user_agent_creator\u001b[38;5;241m=\u001b[39muser_agent_creator,\n\u001b[1;32m    996\u001b[0m )\n\u001b[0;32m--> 997\u001b[0m client \u001b[38;5;241m=\u001b[39m \u001b[43mclient_creator\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcreate_client\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    998\u001b[0m \u001b[43m    \u001b[49m\u001b[43mservice_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mservice_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    999\u001b[0m \u001b[43m    \u001b[49m\u001b[43mregion_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mregion_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1000\u001b[0m \u001b[43m    \u001b[49m\u001b[43mis_secure\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43muse_ssl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1001\u001b[0m \u001b[43m    \u001b[49m\u001b[43mendpoint_url\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mendpoint_url\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1002\u001b[0m \u001b[43m    \u001b[49m\u001b[43mverify\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mverify\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1003\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcredentials\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcredentials\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1004\u001b[0m \u001b[43m    \u001b[49m\u001b[43mscoped_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_scoped_config\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1005\u001b[0m \u001b[43m    \u001b[49m\u001b[43mclient_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1006\u001b[0m \u001b[43m    \u001b[49m\u001b[43mapi_version\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mapi_version\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1007\u001b[0m \u001b[43m    \u001b[49m\u001b[43mauth_token\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mauth_token\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1008\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1009\u001b[0m monitor \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_internal_component(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmonitor\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m   1010\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m monitor \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "File \u001b[0;32m/opt/conda/lib/python3.12/site-packages/botocore/client.py:165\u001b[0m, in \u001b[0;36mClientCreator.create_client\u001b[0;34m(self, service_name, region_name, is_secure, endpoint_url, verify, credentials, scoped_config, api_version, client_config, auth_token)\u001b[0m\n\u001b[1;32m    148\u001b[0m region_name, client_config \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_normalize_fips_region(\n\u001b[1;32m    149\u001b[0m     region_name, client_config\n\u001b[1;32m    150\u001b[0m )\n\u001b[1;32m    151\u001b[0m endpoint_bridge \u001b[38;5;241m=\u001b[39m ClientEndpointBridge(\n\u001b[1;32m    152\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_endpoint_resolver,\n\u001b[1;32m    153\u001b[0m     scoped_config,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    159\u001b[0m     ),\n\u001b[1;32m    160\u001b[0m )\n\u001b[1;32m    161\u001b[0m client_args \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_client_args(\n\u001b[1;32m    162\u001b[0m     service_model,\n\u001b[1;32m    163\u001b[0m     region_name,\n\u001b[1;32m    164\u001b[0m     is_secure,\n\u001b[0;32m--> 165\u001b[0m     endpoint_url,\n\u001b[1;32m    166\u001b[0m     verify,\n\u001b[1;32m    167\u001b[0m     credentials,\n\u001b[1;32m    168\u001b[0m     scoped_config,\n\u001b[1;32m    169\u001b[0m     client_config,\n\u001b[1;32m    170\u001b[0m     endpoint_bridge,\n\u001b[1;32m    171\u001b[0m     auth_token,\n\u001b[1;32m    172\u001b[0m     endpoints_ruleset_data,\n\u001b[1;32m    173\u001b[0m     partition_data,\n\u001b[1;32m    174\u001b[0m )\n\u001b[1;32m    175\u001b[0m service_client \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mcls\u001b[39m(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mclient_args)\n\u001b[1;32m    176\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_register_retries(service_client)\n", "File \u001b[0;32m/opt/conda/lib/python3.12/site-packages/botocore/client.py:524\u001b[0m, in \u001b[0;36m_get_client_args\u001b[0;34m(self, service_model, region_name, is_secure, endpoint_url, verify, credentials, scoped_config, client_config, endpoint_bridge, auth_token, endpoints_ruleset_data, partition_data)\u001b[0m\n\u001b[1;32m    523\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m_create_methods\u001b[39m(\u001b[38;5;28mself\u001b[39m, service_model):\n\u001b[0;32m--> 524\u001b[0m     op_dict \u001b[38;5;241m=\u001b[39m {}\n\u001b[1;32m    525\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m operation_name \u001b[38;5;129;01min\u001b[39;00m service_model\u001b[38;5;241m.\u001b[39moperation_names:\n\u001b[1;32m    526\u001b[0m         py_operation_name \u001b[38;5;241m=\u001b[39m xform_name(operation_name)\n", "File \u001b[0;32m/opt/conda/lib/python3.12/site-packages/botocore/args.py:124\u001b[0m, in \u001b[0;36mget_client_args\u001b[0;34m(self, service_model, region_name, is_secure, endpoint_url, verify, credentials, scoped_config, client_config, endpoint_bridge, auth_token, endpoints_ruleset_data, partition_data)\u001b[0m\n\u001b[1;32m    120\u001b[0m endpoint_region_name \u001b[38;5;241m=\u001b[39m endpoint_config[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mregion_name\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[1;32m    122\u001b[0m event_emitter \u001b[38;5;241m=\u001b[39m copy\u001b[38;5;241m.\u001b[39mcopy(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_event_emitter)\n\u001b[1;32m    123\u001b[0m signer \u001b[38;5;241m=\u001b[39m RequestSigner(\n\u001b[0;32m--> 124\u001b[0m     service_model\u001b[38;5;241m.\u001b[39mservice_id,\n\u001b[1;32m    125\u001b[0m     signing_region,\n\u001b[1;32m    126\u001b[0m     endpoint_config[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124msigning_name\u001b[39m\u001b[38;5;124m'\u001b[39m],\n\u001b[1;32m    127\u001b[0m     endpoint_config[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124msignature_version\u001b[39m\u001b[38;5;124m'\u001b[39m],\n\u001b[1;32m    128\u001b[0m     credentials,\n\u001b[1;32m    129\u001b[0m     event_emitter,\n\u001b[1;32m    130\u001b[0m     auth_token,\n\u001b[1;32m    131\u001b[0m )\n\u001b[1;32m    133\u001b[0m config_kwargs[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124ms3\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m s3_config\n\u001b[1;32m    134\u001b[0m new_config \u001b[38;5;241m=\u001b[39m Config(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mconfig_kwargs)\n", "File \u001b[0;32m/opt/conda/lib/python3.12/site-packages/botocore/args.py:313\u001b[0m, in \u001b[0;36mcompute_client_args\u001b[0;34m(self, service_model, client_config, endpoint_bridge, region_name, endpoint_url, is_secure, scoped_config)\u001b[0m\n\u001b[1;32m      0\u001b[0m <Error retrieving source code with stack_data see ipython/ipython#13598>\n", "File \u001b[0;32m/opt/conda/lib/python3.12/site-packages/botocore/args.py:833\u001b[0m, in \u001b[0;36m_compute_checksum_config\u001b[0;34m(self, config_kwargs)\u001b[0m\n\u001b[1;32m      0\u001b[0m <Error retrieving source code with stack_data see ipython/ipython#13598>\n", "File \u001b[0;32m/opt/conda/lib/python3.12/site-packages/botocore/args.py:875\u001b[0m, in \u001b[0;36m_handle_checksum_config\u001b[0;34m(self, config_kwargs, config_key, valid_options)\u001b[0m\n\u001b[1;32m      0\u001b[0m <Error retrieving source code with stack_data see ipython/ipython#13598>\n", "\u001b[0;31mInvalidChecksumConfigError\u001b[0m: Unsupported configuration value for request_checksum_calculation. Expected one of ('when_supported', 'when_required') but got None."]}], "source": ["boto3_bedrock = bedrock.get_bedrock_client(\n", "    assumed_role=os.environ.get(\"BEDROCK_ASSUME_ROLE\", None),\n", "    region=os.environ.get(\"AWS_DEFAULT_REGION\", None)\n", ")"]}, {"cell_type": "code", "execution_count": 19, "id": "f82570d3-e5f9-4a2f-aa53-32f929308dc9", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["BEDROCK_ASSUME_ROLE: None\n", "AWS_DEFAULT_REGION: us-west-2\n"]}], "source": ["import os\n", "\n", "print(\"BEDROCK_ASSUME_ROLE:\", os.environ.get(\"BEDROCK_ASSUME_ROLE\"))\n", "print(\"AWS_DEFAULT_REGION:\", os.environ.get(\"AWS_DEFAULT_REGION\"))"]}, {"cell_type": "code", "execution_count": 20, "id": "3862f12d-32d4-42aa-805a-4b8532936df3", "metadata": {"tags": []}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"BEDROCK_ASSUME_ROLE\"] = \"arn:aws:iam::************:role/MyBedrockRole\""]}, {"cell_type": "code", "execution_count": 21, "id": "16b590c4-9ec5-4bc1-b8ce-767bc98c36e3", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["arn:aws:iam::************:role/MyBedrockRole\n", "us-west-2\n"]}], "source": ["# Now, check if they are set\n", "print(os.environ.get(\"BEDROCK_ASSUME_ROLE\"))  # Should print the role ARN\n", "print(os.environ.get(\"AWS_DEFAULT_REGION\"))   # Should print 'us-west-2'"]}, {"cell_type": "code", "execution_count": 22, "id": "5bc310fc-9703-459b-9d84-b0830612bad3", "metadata": {"tags": []}, "outputs": [], "source": ["# import boto3\n", "\n", "# iam_client = boto3.client(\"iam\")\n", "\n", "# role_name = \"SSFCMGOASIS_PROD_NBA_MEDICAL_ANALYST_BR\"  # Replace with your role name\n", "\n", "# try:\n", "#     response = iam_client.get_role(RoleName=role_name)\n", "#     print(\"Role exists:\", response[\"Role\"])\n", "# except iam_client.exceptions.NoSuchEntityException:\n", "#     print(\"Role does not exist.\")"]}, {"cell_type": "code", "execution_count": 23, "id": "ad592007-e4b5-411f-9ddc-153eb703c2e9", "metadata": {"tags": []}, "outputs": [], "source": ["def get_bedrock_client(*args, **kwargs):\n", "    if 'aws_account_id' in kwargs:\n", "        del kwargs['aws_account_id']  # Remove unsupported argument\n", "    return boto3.client('bedrock', *args, **kwargs)"]}, {"cell_type": "code", "execution_count": 24, "id": "6edc1b68-33b3-45e6-af16-de6292eca279", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Arguments passed to get_bedrock_client: {'assumed_role': 'arn:aws:iam::************:role/MyBedrockRole', 'region': 'us-west-2'}\n"]}], "source": ["import os\n", "\n", "print(\"Arguments passed to get_bedrock_client:\", {\n", "    \"assumed_role\": os.environ.get(\"BEDROCK_ASSUME_ROLE\"),\n", "    \"region\": os.environ.get(\"AWS_DEFAULT_REGION\")\n", "})"]}, {"cell_type": "code", "execution_count": 25, "id": "7ee45d86-1672-4935-b03e-69e68990e2c1", "metadata": {}, "outputs": [], "source": ["# !pip install --upgrade boto3 botocore"]}, {"cell_type": "code", "execution_count": 26, "id": "aa29d785-8b36-4a71-be79-af7eb6d6c944", "metadata": {}, "outputs": [], "source": ["# pip install boto3==1.34.0 botocore==1.34.0"]}, {"cell_type": "code", "execution_count": 27, "id": "f51baf79-37ef-45b1-b812-9325dbba095c", "metadata": {}, "outputs": [{"ename": "InvalidChecksumConfigError", "evalue": "Unsupported configuration value for request_checksum_calculation. Expected one of ('when_supported', 'when_required') but got None.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mInvalidChecksumConfigError\u001b[0m                Traceback (most recent call last)", "Cell \u001b[0;32mIn[27], line 6\u001b[0m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mboto3\u001b[39;00m\n\u001b[1;32m      4\u001b[0m config \u001b[38;5;241m=\u001b[39m Config(region_name\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mus-west-2\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m----> 6\u001b[0m bedrock_client \u001b[38;5;241m=\u001b[39m \u001b[43mboto3\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mclient\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mbedrock\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconfig\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mconfig\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.12/site-packages/boto3/__init__.py:92\u001b[0m, in \u001b[0;36mclient\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m     86\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mclient\u001b[39m(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m     87\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m     88\u001b[0m \u001b[38;5;124;03m    Create a low-level service client by name using the default session.\u001b[39;00m\n\u001b[1;32m     89\u001b[0m \n\u001b[1;32m     90\u001b[0m \u001b[38;5;124;03m    See :py:meth:`boto3.session.Session.client`.\u001b[39;00m\n\u001b[1;32m     91\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m---> 92\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_get_default_session\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mclient\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.12/site-packages/boto3/session.py:299\u001b[0m, in \u001b[0;36mSession.client\u001b[0;34m(self, service_name, region_name, api_version, use_ssl, verify, endpoint_url, aws_access_key_id, aws_secret_access_key, aws_session_token, config)\u001b[0m\n\u001b[1;32m    217\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mclient\u001b[39m(\n\u001b[1;32m    218\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m    219\u001b[0m     service_name,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    228\u001b[0m     config\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m    229\u001b[0m ):\n\u001b[1;32m    230\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    231\u001b[0m \u001b[38;5;124;03m    Create a low-level service client by name.\u001b[39;00m\n\u001b[1;32m    232\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    297\u001b[0m \n\u001b[1;32m    298\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 299\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_session\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcreate_client\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    300\u001b[0m \u001b[43m        \u001b[49m\u001b[43mservice_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    301\u001b[0m \u001b[43m        \u001b[49m\u001b[43mregion_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mregion_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    302\u001b[0m \u001b[43m        \u001b[49m\u001b[43mapi_version\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mapi_version\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    303\u001b[0m \u001b[43m        \u001b[49m\u001b[43muse_ssl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43muse_ssl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    304\u001b[0m \u001b[43m        \u001b[49m\u001b[43mverify\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mverify\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    305\u001b[0m \u001b[43m        \u001b[49m\u001b[43mendpoint_url\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mendpoint_url\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    306\u001b[0m \u001b[43m        \u001b[49m\u001b[43maws_access_key_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maws_access_key_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    307\u001b[0m \u001b[43m        \u001b[49m\u001b[43maws_secret_access_key\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maws_secret_access_key\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    308\u001b[0m \u001b[43m        \u001b[49m\u001b[43maws_session_token\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maws_session_token\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    309\u001b[0m \u001b[43m        \u001b[49m\u001b[43mconfig\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    310\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.12/site-packages/botocore/session.py:997\u001b[0m, in \u001b[0;36mSession.create_client\u001b[0;34m(self, service_name, region_name, api_version, use_ssl, verify, endpoint_url, aws_access_key_id, aws_secret_access_key, aws_session_token, config)\u001b[0m\n\u001b[1;32m    980\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_add_configured_endpoint_provider(\n\u001b[1;32m    981\u001b[0m     client_name\u001b[38;5;241m=\u001b[39mservice_name,\n\u001b[1;32m    982\u001b[0m     config_store\u001b[38;5;241m=\u001b[39mconfig_store,\n\u001b[1;32m    983\u001b[0m )\n\u001b[1;32m    985\u001b[0m client_creator \u001b[38;5;241m=\u001b[39m botocore\u001b[38;5;241m.\u001b[39mclient\u001b[38;5;241m.\u001b[39mClientCreator(\n\u001b[1;32m    986\u001b[0m     loader,\n\u001b[1;32m    987\u001b[0m     endpoint_resolver,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    995\u001b[0m     user_agent_creator\u001b[38;5;241m=\u001b[39muser_agent_creator,\n\u001b[1;32m    996\u001b[0m )\n\u001b[0;32m--> 997\u001b[0m client \u001b[38;5;241m=\u001b[39m \u001b[43mclient_creator\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcreate_client\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    998\u001b[0m \u001b[43m    \u001b[49m\u001b[43mservice_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mservice_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    999\u001b[0m \u001b[43m    \u001b[49m\u001b[43mregion_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mregion_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1000\u001b[0m \u001b[43m    \u001b[49m\u001b[43mis_secure\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43muse_ssl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1001\u001b[0m \u001b[43m    \u001b[49m\u001b[43mendpoint_url\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mendpoint_url\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1002\u001b[0m \u001b[43m    \u001b[49m\u001b[43mverify\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mverify\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1003\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcredentials\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcredentials\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1004\u001b[0m \u001b[43m    \u001b[49m\u001b[43mscoped_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_scoped_config\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1005\u001b[0m \u001b[43m    \u001b[49m\u001b[43mclient_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1006\u001b[0m \u001b[43m    \u001b[49m\u001b[43mapi_version\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mapi_version\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1007\u001b[0m \u001b[43m    \u001b[49m\u001b[43mauth_token\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mauth_token\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1008\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1009\u001b[0m monitor \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_internal_component(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmonitor\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m   1010\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m monitor \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "File \u001b[0;32m/opt/conda/lib/python3.12/site-packages/botocore/client.py:165\u001b[0m, in \u001b[0;36mClientCreator.create_client\u001b[0;34m(self, service_name, region_name, is_secure, endpoint_url, verify, credentials, scoped_config, api_version, client_config, auth_token)\u001b[0m\n\u001b[1;32m    148\u001b[0m region_name, client_config \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_normalize_fips_region(\n\u001b[1;32m    149\u001b[0m     region_name, client_config\n\u001b[1;32m    150\u001b[0m )\n\u001b[1;32m    151\u001b[0m endpoint_bridge \u001b[38;5;241m=\u001b[39m ClientEndpointBridge(\n\u001b[1;32m    152\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_endpoint_resolver,\n\u001b[1;32m    153\u001b[0m     scoped_config,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    159\u001b[0m     ),\n\u001b[1;32m    160\u001b[0m )\n\u001b[1;32m    161\u001b[0m client_args \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_client_args(\n\u001b[1;32m    162\u001b[0m     service_model,\n\u001b[1;32m    163\u001b[0m     region_name,\n\u001b[1;32m    164\u001b[0m     is_secure,\n\u001b[0;32m--> 165\u001b[0m     endpoint_url,\n\u001b[1;32m    166\u001b[0m     verify,\n\u001b[1;32m    167\u001b[0m     credentials,\n\u001b[1;32m    168\u001b[0m     scoped_config,\n\u001b[1;32m    169\u001b[0m     client_config,\n\u001b[1;32m    170\u001b[0m     endpoint_bridge,\n\u001b[1;32m    171\u001b[0m     auth_token,\n\u001b[1;32m    172\u001b[0m     endpoints_ruleset_data,\n\u001b[1;32m    173\u001b[0m     partition_data,\n\u001b[1;32m    174\u001b[0m )\n\u001b[1;32m    175\u001b[0m service_client \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mcls\u001b[39m(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mclient_args)\n\u001b[1;32m    176\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_register_retries(service_client)\n", "File \u001b[0;32m/opt/conda/lib/python3.12/site-packages/botocore/client.py:524\u001b[0m, in \u001b[0;36m_get_client_args\u001b[0;34m(self, service_model, region_name, is_secure, endpoint_url, verify, credentials, scoped_config, client_config, endpoint_bridge, auth_token, endpoints_ruleset_data, partition_data)\u001b[0m\n\u001b[1;32m    523\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m_create_methods\u001b[39m(\u001b[38;5;28mself\u001b[39m, service_model):\n\u001b[0;32m--> 524\u001b[0m     op_dict \u001b[38;5;241m=\u001b[39m {}\n\u001b[1;32m    525\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m operation_name \u001b[38;5;129;01min\u001b[39;00m service_model\u001b[38;5;241m.\u001b[39moperation_names:\n\u001b[1;32m    526\u001b[0m         py_operation_name \u001b[38;5;241m=\u001b[39m xform_name(operation_name)\n", "File \u001b[0;32m/opt/conda/lib/python3.12/site-packages/botocore/args.py:124\u001b[0m, in \u001b[0;36mget_client_args\u001b[0;34m(self, service_model, region_name, is_secure, endpoint_url, verify, credentials, scoped_config, client_config, endpoint_bridge, auth_token, endpoints_ruleset_data, partition_data)\u001b[0m\n\u001b[1;32m    120\u001b[0m endpoint_region_name \u001b[38;5;241m=\u001b[39m endpoint_config[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mregion_name\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[1;32m    122\u001b[0m event_emitter \u001b[38;5;241m=\u001b[39m copy\u001b[38;5;241m.\u001b[39mcopy(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_event_emitter)\n\u001b[1;32m    123\u001b[0m signer \u001b[38;5;241m=\u001b[39m RequestSigner(\n\u001b[0;32m--> 124\u001b[0m     service_model\u001b[38;5;241m.\u001b[39mservice_id,\n\u001b[1;32m    125\u001b[0m     signing_region,\n\u001b[1;32m    126\u001b[0m     endpoint_config[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124msigning_name\u001b[39m\u001b[38;5;124m'\u001b[39m],\n\u001b[1;32m    127\u001b[0m     endpoint_config[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124msignature_version\u001b[39m\u001b[38;5;124m'\u001b[39m],\n\u001b[1;32m    128\u001b[0m     credentials,\n\u001b[1;32m    129\u001b[0m     event_emitter,\n\u001b[1;32m    130\u001b[0m     auth_token,\n\u001b[1;32m    131\u001b[0m )\n\u001b[1;32m    133\u001b[0m config_kwargs[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124ms3\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m s3_config\n\u001b[1;32m    134\u001b[0m new_config \u001b[38;5;241m=\u001b[39m Config(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mconfig_kwargs)\n", "File \u001b[0;32m/opt/conda/lib/python3.12/site-packages/botocore/args.py:313\u001b[0m, in \u001b[0;36mcompute_client_args\u001b[0;34m(self, service_model, client_config, endpoint_bridge, region_name, endpoint_url, is_secure, scoped_config)\u001b[0m\n\u001b[1;32m      0\u001b[0m <Error retrieving source code with stack_data see ipython/ipython#13598>\n", "File \u001b[0;32m/opt/conda/lib/python3.12/site-packages/botocore/args.py:833\u001b[0m, in \u001b[0;36m_compute_checksum_config\u001b[0;34m(self, config_kwargs)\u001b[0m\n\u001b[1;32m      0\u001b[0m <Error retrieving source code with stack_data see ipython/ipython#13598>\n", "File \u001b[0;32m/opt/conda/lib/python3.12/site-packages/botocore/args.py:875\u001b[0m, in \u001b[0;36m_handle_checksum_config\u001b[0;34m(self, config_kwargs, config_key, valid_options)\u001b[0m\n\u001b[1;32m      0\u001b[0m <Error retrieving source code with stack_data see ipython/ipython#13598>\n", "\u001b[0;31mInvalidChecksumConfigError\u001b[0m: Unsupported configuration value for request_checksum_calculation. Expected one of ('when_supported', 'when_required') but got None."]}], "source": ["from botocore.config import Config\n", "import boto3\n", "\n", "config = Config(region_name=\"us-west-2\")\n", "\n", "bedrock_client = boto3.client(\"bedrock\", config=config)"]}, {"cell_type": "code", "execution_count": 28, "id": "ebbb636f-29ac-4cb1-b561-92dd7237aebb", "metadata": {"tags": []}, "outputs": [], "source": ["bedrock_client = boto3.client('bedrock', region_name=os.environ.get(\"AWS_DEFAULT_REGION\"))"]}, {"cell_type": "code", "execution_count": 29, "id": "c55c891c-a454-4291-ab9b-1336ea091c33", "metadata": {"tags": []}, "outputs": [], "source": ["# pip show boto3 botocore"]}, {"cell_type": "code", "execution_count": 30, "id": "27d2a762-504a-4e1c-9d0e-69c4d35dbc00", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_2176/1154316981.py:7: LangChainDeprecationWarning: The class `Bedrock` was deprecated in LangChain 0.0.34 and will be removed in 1.0. An updated version of the class exists in the :class:`~langchain-aws package and should be used instead. To use it run `pip install -U :class:`~langchain-aws` and import as `from :class:`~langchain_aws import BedrockLLM``.\n", "  cohere_llm= Bedrock(\n"]}], "source": ["inference_modifier = {\n", "    \"temperature\": 0.5,\n", "    \"p\": 0.9,\n", "    \"max_tokens\": 4096\n", "}\n", "\n", "cohere_llm= Bedrock(\n", "    model_id=\"cohere.command-text-v14\",\n", "    client=boto3_bedrock,\n", "    model_kwargs=inference_modifier,\n", ")"]}, {"cell_type": "code", "execution_count": 31, "id": "5fa6953d-de41-42c2-af7c-0b7611dc16b3", "metadata": {"tags": []}, "outputs": [], "source": ["anthropic_llm =ChatBedrock(\n", "                    model_id=\"anthropic.claude-3-sonnet-20240229-v1:0\",\n", "                    model_kwargs={\"temperature\": 0.7,\"top_p\":0.95,'max_tokens':2048},\n", "                ) "]}, {"cell_type": "code", "execution_count": 32, "id": "ad052c0c-bf42-4d6f-8a83-d043476506cd", "metadata": {"tags": []}, "outputs": [], "source": ["#cohere_llm.invoke('tell me  a joke')"]}, {"cell_type": "code", "execution_count": 33, "id": "32c98304-3a7b-4ca5-bffa-596163e2ed52", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["\"Here's a silly joke for you:\\n\\nWhy can't a bicycle stand up by itself?\\nIt's two-tired!\""]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["anthropic_llm.invoke('tell me a joke').content"]}, {"cell_type": "code", "execution_count": 34, "id": "cc250e39-fd48-47b4-9127-1ba57fda0df2", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["AIMessage(content=\"Here's a joke for you:\\n\\nWhy can't a bicycle stand up by itself? Because it's two-tired!\", additional_kwargs={'usage': {'prompt_tokens': 11, 'completion_tokens': 28, 'cache_read_input_tokens': 0, 'cache_write_input_tokens': 0, 'total_tokens': 39}, 'stop_reason': 'end_turn', 'thinking': {}, 'model_id': 'anthropic.claude-3-sonnet-20240229-v1:0', 'model_name': 'anthropic.claude-3-sonnet-20240229-v1:0'}, response_metadata={'usage': {'prompt_tokens': 11, 'completion_tokens': 28, 'cache_read_input_tokens': 0, 'cache_write_input_tokens': 0, 'total_tokens': 39}, 'stop_reason': 'end_turn', 'thinking': {}, 'model_id': 'anthropic.claude-3-sonnet-20240229-v1:0', 'model_name': 'anthropic.claude-3-sonnet-20240229-v1:0'}, id='run-87f45ffe-c753-4140-a097-5f0bff6a932d-0', usage_metadata={'input_tokens': 11, 'output_tokens': 28, 'total_tokens': 39, 'input_token_details': {'cache_creation': 0, 'cache_read': 0}})"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["anthropic_llm.invoke('tell me a joke')"]}, {"cell_type": "code", "execution_count": 35, "id": "55a4d221-e9b3-44b2-9a22-cbcf6e1301c2", "metadata": {"tags": []}, "outputs": [], "source": ["#SQL connection"]}, {"cell_type": "code", "execution_count": 36, "id": "113d9b83-d20e-4a71-8ae3-e30d69493cae", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["'/mnt/custom-file-systems/efs/fs-09d18f54efda548e0_fsap-0d2d5aa2d40d94fed/React_LLM/ReAct_SQL_New'"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["pwd"]}, {"cell_type": "code", "execution_count": 37, "id": "6175129d-d2c6-402a-8bbe-bf71ac9c6d24", "metadata": {"tags": []}, "outputs": [], "source": ["AWS_REGION = \"us-west-2\" # Change me\n", "SCHEMA_NAME = \"oasis_normalized\" # Athena calls this a database\n", "S3_STAGING_DIR = \"s3://cmg-oasis-prod-sandbox-nba-medical-analyst/VeevaLink_RAG_SQL/staging_directory/\" # Change me"]}, {"cell_type": "code", "execution_count": 38, "id": "4994b920-d635-4ef4-8be5-8f8b623cca69", "metadata": {"tags": []}, "outputs": [], "source": ["connect_str = \"awsathena+rest://athena.{region_name}.amazonaws.com:443/{schema_name}?s3_staging_dir={s3_staging_dir}\""]}, {"cell_type": "code", "execution_count": 39, "id": "31903fef-a32c-4782-bbb4-40490c77a39e", "metadata": {"tags": []}, "outputs": [], "source": ["engine = create_engine(connect_str.format(\n", "        region_name=AWS_REGION,\n", "        schema_name=SCHEMA_NAME,\n", "        s3_staging_dir=quote_plus(S3_STAGING_DIR)\n", "))"]}, {"cell_type": "code", "execution_count": 40, "id": "c8634e0c-9e91-4c04-80fe-e07011a8ae99", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["Engine(awsathena+rest://athena.us-west-2.amazonaws.com:443/oasis_normalized?s3_staging_dir=s3%3A%2F%2Fcmg-oasis-prod-sandbox-nba-medical-analyst%2FVeevaLink_RAG_SQL%2Fstaging_directory%2F)"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["engine"]}, {"cell_type": "code", "execution_count": 41, "id": "b1963f8a-c796-4e91-96fe-d3d6d8687571", "metadata": {"tags": []}, "outputs": [], "source": ["db = SQLDatabase(engine)"]}, {"cell_type": "code", "execution_count": 42, "id": "2c0bd3c0-1fa5-4776-8673-ca24b630fea9", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["<langchain_community.utilities.sql_database.SQLDatabase at 0x7fca65615370>"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["db"]}, {"cell_type": "code", "execution_count": 43, "id": "52b3130d-a0f0-4066-8cbf-0ff13f94875a", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a<PERSON><PERSON><PERSON>\n"]}], "source": ["print(db.dialect)"]}, {"cell_type": "code", "execution_count": 44, "id": "5beb8a60-a8f2-43e1-86cf-fd15e651c6c1", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['access_metric_fact_monthly', 'access_to_care', 'affinity_monitor', 'alert_iqvia_internal_daily', 'alert_iqvia_internal_monthly', 'alert_iqvia_internal_weekly', 'alignment_account_to_territory', 'alignment_account_to_territory_daily', 'alignment_account_to_territory_weekly', 'alignment_brand_credit_rules', 'alignment_brand_to_position', 'alignment_brand_to_position_daily', 'alignment_brand_to_position_weekly', 'alignment_brand_to_therapeutic_area', 'alignment_brand_to_therapeutic_area_daily', 'alignment_brand_to_therapeutic_area_weekly', 'alignment_ecosystem_dim', 'alignment_ecosystem_dim_daily', 'alignment_ecosystem_dim_weekly', 'alignment_ecosystem_hierarchy', 'alignment_ecosystem_hierarchy_daily', 'alignment_ecosystem_hierarchy_history_daily', 'alignment_ecosystem_hierarchy_weekly', 'alignment_employee_dim', 'alignment_employee_dim_daily', 'alignment_employee_dim_weekly', 'alignment_field_force_dim', 'alignment_field_force_dim_daily', 'alignment_field_force_dim_weekly', 'alignment_field_force_hierarchy', 'alignment_field_force_hierarchy_daily', 'alignment_field_force_hierarchy_weekly', 'alignment_hcp_outlet_credit_flag', 'alignment_hcp_outlet_credit_flag_weekly', 'alignment_hcp_outlet_exception_credit_flag', 'alignment_roster_hierarchy_daily', 'alignment_sales_area_relationship_asc', 'alignment_sales_area_relationship_asc_daily', 'alignment_sales_area_relationship_asc_weekly', 'alignment_sales_area_relationship_roster_daily', 'alignment_territory_hierarchy', 'alignment_territory_hierarchy_daily', 'alignment_territory_hierarchy_weekly', 'alignment_territory_therapeutic_area_allocation_daily', 'alignment_user_assignment_allocation_daily', 'alignment_user_to_field_title', 'alignment_user_to_field_title_daily', 'alignment_user_to_field_title_weekly', 'alignment_zip_to_ecosystem', 'alignment_zip_to_ecosystem_daily', 'alignment_zip_to_ecosystem_weekly', 'alignment_zip_to_position', 'alignment_zip_to_position_daily', 'alignment_zip_to_position_weekly', 'alignments_field_force_brand_asc', 'anonymized_pdv_oasis_voc_medical_patient_quest_resp_weekly', 'anonymized_pdv_synapse_inquiry', 'benefit_map_brand_national', 'bioonc_soc_classification', 'bob_adjustment_factor_national', 'bob_mix_sales_national', 'bob_sales_appended_national', 'bob_sales_mix_ecosystem_national', 'bob_sales_mix_zip3_national', 'brand_book_of_business', 'brand_book_of_business_national', 'brand_hcp_affiliations', 'brand_ndc', 'care_cem_hemlibra_interaction_hist', 'care_cem_sp_list', 'care_frm_pact_case_l', 'care_hco_cot_sites', 'care_hco_tier', 'care_hcp_sma_fao', 'cdm_brnd_to_ther_area', 'cdm_emp_dim', 'claim_split', 'clinical_trials_normalized', 'crm_account', 'crm_account_address', 'crm_medical_approved_email', 'crm_medical_event_based_customer_list', 'crm_medical_inquiry_request', 'crm_medical_insights', 'crm_medical_interactions', 'crm_medical_priority_customer_list', 'crm_medical_standing_requests', 'crm_medical_survey', 'crm_medical_territory_sales_force', 'crm_user_profile', 'drg_pbm_zip_lives', 'drg_spcm_pbm_entity_bridge', 'ecosystem_dim', 'ecosystem_dim_weekly', 'ecosystem_hier', 'fba_sap_outs_fact', 'finance_brand_ndc_eff_wac_price_monthly', 'finance_ecosystem_actual_vs_forecast', 'finance_ecosystem_actual_vs_forecast_2021', 'finance_ecosystem_gross_and_net_actuals', 'finance_ecosystem_pnl', 'finance_uslt_pnl', 'g2o_payer_iqvia_idm_sales_monthly', 'hcp360_activities', 'hcp360_activities_content', 'hcp360_activities_lftest', 'hcp360_profile_addresses', 'hcp360_profile_identifier_communication', 'hcp360_profile_relationships', 'hcp360_qualification_config', 'hcp_omni_clinical_trials_acronym_bimonthly', 'hcp_omni_clinical_trials_acronym_bimonthly_outbound', 'hcp_omni_clinical_trials_acronym_feedback_bimonthly_outbound', 'hcp_omni_clinical_trials_acronym_response_bimonthly', 'hcp_omni_clinical_trials_bimonthly', 'hcp_omni_clinical_trials_bimonthly_outbound', 'hcp_omni_medical_insights_daily', 'hcp_omni_medical_insights_daily_outbound', 'hcp_omni_medical_interaction_discussions_daily', 'hcp_omni_medical_interaction_discussions_daily_outbound', 'hcp_omni_medical_interactions_daily', 'hcp_omni_medical_interactions_daily_outbound', 'hcp_omni_medical_standing_request_daily', 'hcp_omni_medical_standing_request_daily_outbound', 'hcp_omni_medical_survey_daily', 'hcp_omni_medical_survey_daily_outbound', 'hcp_omni_voc_medallia_medical_daily', 'hcp_omni_voc_medallia_medical_daily_outbound', 'hcp_outlet_affiliations', 'hcp_outlet_affiliations_bimonthly', 'hcp_outlet_affiliations_weekly', 'hcp_outlet_alignments', 'hcp_outlet_alignments_weekly', 'hcp_outlet_field_force_alignments', 'hcp_outlet_field_force_alignments_weekly', 'health_outcomes', 'healthcare_demographics_by_geography', 'idn_provider_site_sales_national', 'incidence_prevalence_by_indication', 'incidence_prevalence_by_indication_historical_2024', 'interaction', 'interim_ra_mean_adjusted_group_lvl', 'interim_ra_nsp_adjusted_weight', 'iqvia_alerts_crosswalk_weekly', 'iqvia_alerts_metric_crosswalk_weekly', 'iqvia_alerts_metric_weekly', 'iqvia_alerts_weekly', 'iqvia_hoa_crosswalk_monthly', 'iqvia_restricted_outlets', 'iqvia_restricted_outlets_staging', 'kol_ecosystem_classification', 'lives_fact', 'market_share_split_factor_monthly', 'master_cm_mdm_profile', 'master_cm_mdm_profile_bimonthly', 'master_cm_mdm_profile_weekly', 'master_cm_org_profile', 'master_cm_org_profile_weekly', 'master_cross_portfolio_mapping', 'master_metric_market_share_config', 'master_pm_brand', 'master_pm_brand_asc', 'master_pm_exception', 'master_pm_indication_asc', 'master_pm_indications', 'master_pm_product', 'master_pm_product_asc', 'master_pm_product_brand_report_asc', 'master_pm_product_indication_relationship', 'master_pm_product_xref', 'master_pm_scientific_area', 'master_therapeutic_area_mapping', 'master_twitter_conference_name_mapping', 'master_zip_geography_mapping', 'master_zip_geography_mapping_historical_2024', 'mdm_access_dim', 'mdm_mco_src_xref', 'mdm_payer_access_dim', 'mdm_payer_hierarchy', 'mdm_pbm_hierarchy', 'mdm_plan_hierarchy', 'mdm_plan_hierarchy_dim', 'medical_activity_based_customer_list', 'medical_congress_publication', 'meeting_conventions_medical_daily', 'meeting_conventions_tracker', 'national_bob_mix_brand_rebate_adj', 'norm_pct_data_refresh_details', 'nrm_mdm_geo', 'oasis_10417_mdm_access_dim_uat', 'oasis_payer_iqvia_bkp', 'oasis_payer_iqvia_new', 'oce_medical_inquiry_request', 'oce_medical_insights', 'oce_medical_interactions', 'oce_medical_standing_requests', 'oce_medical_survey', 'omni_patient_cohort_claims', 'omni_patient_lot_aggregate', 'omni_patient_lot_episode', 'org_alignments', 'org_alignments_weekly', 'org_field_force_alignments', 'org_field_force_alignments_weekly', 'org_lmt_assignment', 'pact_hco_staging_history_weekly', 'pact_hcp_affiliations_staging_history_weekly', 'payer_360_master_mco_profile', 'payer_360_mco_hierarchy', 'payer_formulary_disadvantage_weight', 'payer_formulary_policy_dim', 'payer_formulary_policy_dim_ahr', 'payer_formulary_policy_dim_ahr_12_11', 'payer_formulary_policy_dim_bkp', 'payer_formulary_policy_dim_history', 'payer_formulary_policy_lives', 'payer_iqvia_idm_sales_monthly', 'payer_iqvia_idm_sales_weekly', 'payer_lives_monthly', 'payer_mix_national', 'payer_override', 'payer_pbm_info_national', 'payer_policy_lives', 'payer_policy_lives_test', 'payer_segment_formulary_policy_dim', 'payer_source_formulary_policy_dim', 'payer_source_formulary_policy_dim_partition', 'pbm_mapping_info_national', 'pbm_payer_lives_monthly', 'pct_careset_claims_data', 'pct_spear_provider_patient', 'persona_hcp_profile_weekly', 'population_demographics', 'ptnt_cf_brnd_fact', 'pto_hpm_config', 'pto_key_accounts', 'pto_key_accounts_phesgo', 'pto_market_basket', 'pto_market_basket_ahr', 'pto_optha_target_list', 'pto_product_desc', 'pto_target_flag', 'pto_tec_analytical_brand', 'rts_characteristic_indication', 'rts_product_universe', 'rts_scientific_area', 'rts_tiered_indications', 'site_medical_pharmacy_sales_national', 'site_sales_brand_national_imputed_monthly', 'site_sales_flag_national', 'site_sales_monthly_national', 'symphony_site_alerts_weekly', 'synapse_hcp_inquiry', 'synapse_medical_content', 'user_to_ecosystem_hier', 'usma_ct_collaborator', 'usma_ct_indication', 'usma_ct_pi', 'usma_ct_product', 'usma_ct_site', 'usma_ct_sponsor_name_type', 'usma_ct_study_details', 'usma_ct_study_site', 'veeva_link_address_weekly', 'veeva_link_association_weekly', 'veeva_link_clinical_leader_weekly', 'veeva_link_clinical_trial_weekly', 'veeva_link_company_collaboration_weekly', 'veeva_link_company_sponsorship_weekly', 'veeva_link_digital_reach_focus_area_weekly', 'veeva_link_event_attendee_weekly', 'veeva_link_field_favorites_weekly', 'veeva_link_focus_area_weekly', 'veeva_link_gene_link_mdm_match_qtrly', 'veeva_link_index_weekly', 'veeva_link_medical_event_weekly', 'veeva_link_payment_weekly', 'veeva_link_publication_weekly', 'veeva_link_scientific_activity_counts_weekly', 'veeva_link_scientific_expert_changelog_weekly', 'veeva_link_scientific_expert_clinical_trial_weekly', 'veeva_link_scientific_expert_publication_weekly', 'veeva_link_scientific_expert_weekly', 'veeva_link_scientificreach_mapping_weekly', 'voc_anonymized_pdv_oasis_medical_patient_quest_resp_weekly', 'voc_contact_center_synapse_daily', 'voc_field_post_interaction_medical_daily', 'voc_survey_attributes', 'wk_to_445_mth_dim', 'zip_to_dma']\n"]}], "source": ["print(db.get_usable_table_names())"]}, {"cell_type": "code", "execution_count": 45, "id": "b1e04d2a-1aa2-4f23-bb7e-d4da1d370179", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["290"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["len(db.get_usable_table_names())"]}, {"cell_type": "code", "execution_count": 46, "id": "38fe3237-16aa-4771-b917-d6cbed44ec9f", "metadata": {"tags": []}, "outputs": [], "source": ["#print(db.table_info)"]}, {"cell_type": "code", "execution_count": 47, "id": "22d2bc13-7da9-4508-9e76-9ede2e0d9090", "metadata": {"tags": []}, "outputs": [], "source": ["#Agent"]}, {"cell_type": "code", "execution_count": 48, "id": "e90a7b19-26e3-4d42-9bba-bc57614e88b2", "metadata": {"tags": []}, "outputs": [], "source": ["class Table(BaseModel):\n", "    \"\"\"Table in SQL database.\"\"\"\n", "\n", "    name: str = Field(description=\"Name of table in SQL database.\")"]}, {"cell_type": "code", "execution_count": 49, "id": "2d5b0fc9-cf2f-4436-86e1-9ee9852b7af0", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/mnt/custom-file-systems/efs/fs-09d18f54efda548e0_fsap-0d2d5aa2d40d94fed/React_LLM/ReAct_SQL_New/lc_community/tools/new_tools.py:128: LangChainDeprecationWarning: The class `LLMChain` was deprecated in LangChain 0.1.17 and will be removed in 1.0. Use :meth:`~RunnableSequence, e.g., `prompt | llm`` instead.\n", "  values[\"llm_chain\"] = LLMChain(\n"]}, {"data": {"text/plain": ["[QuerySQLDataBaseTool(description=\"Input to this tool is a detailed and correct SQL query, output is a result from the database. If the query is not correct, an error message will be returned. If an error is returned, rewrite the query, check the query, and try again. If you encounter an issue with Unknown column 'xxxx' in 'field list', use sql_db_schema to query the correct table fields.\", db=<langchain_community.utilities.sql_database.SQLDatabase object at 0x7fca65615370>),\n", " InfoSQLDatabaseTool(description='Input to this tool is a comma-separated list of tables, output is the schema and sample rows for those tables. Be sure that the tables actually exist by calling sql_db_list_tables first! Example Input: table1, table2, table3', db=<langchain_community.utilities.sql_database.SQLDatabase object at 0x7fca65615370>),\n", " ListSQLDatabaseTool(db=<langchain_community.utilities.sql_database.SQLDatabase object at 0x7fca65615370>),\n", " QuerySQLCheckerTool(description='Use this tool to double check if your query is correct before executing it. Always use this tool before executing a query with sql_db_query!', db=<langchain_community.utilities.sql_database.SQLDatabase object at 0x7fca65615370>, llm=ChatBedrock(client=<botocore.client.BedrockRuntime object at 0x7fca65c4cbf0>, model_id='anthropic.claude-3-sonnet-20240229-v1:0', model_kwargs={'top_p': 0.95}, temperature=0.7, max_tokens=2048), llm_chain=LLMChain(verbose=False, prompt=PromptTemplate(input_variables=['dialect', 'query'], input_types={}, partial_variables={}, template='\\n{query}\\nDouble check the {dialect} query above for common mistakes, including:\\n- Using NOT IN with NULL values\\n- Using UNION when UNION ALL should have been used\\n- Using BETWEEN for exclusive ranges\\n- Data type mismatch in predicates\\n- Properly quoting identifiers\\n- Using the correct number of arguments for functions\\n- Casting to the correct data type\\n- Using the proper columns for joins\\n\\nIf there are any of the above mistakes, rewrite the query. If there are no mistakes, just reproduce the original query.\\n\\nOutput the final SQL query only.\\n\\nSQL Query: '), llm=ChatBedrock(client=<botocore.client.BedrockRuntime object at 0x7fca65c4cbf0>, model_id='anthropic.claude-3-sonnet-20240229-v1:0', model_kwargs={'top_p': 0.95}, temperature=0.7, max_tokens=2048), output_parser=StrOutputParser(), llm_kwargs={}))]"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "toolkit = SQLDatabaseToolkit(db=db, llm=anthropic_llm)\n", "\n", "tools = toolkit.get_tools()\n", "\n", "tools"]}, {"cell_type": "code", "execution_count": 50, "id": "8640458b-7b8e-4465-9b4c-d7ebbda3029b", "metadata": {"tags": []}, "outputs": [], "source": ["table_names = \"\\n\".join(db.get_usable_table_names())"]}, {"cell_type": "code", "execution_count": 51, "id": "edc93cc1-1088-4902-bcdc-7b05ff7bcaa2", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["'access_metric_fact_monthly\\naccess_to_care\\naffinity_monitor\\nalert_iqvia_internal_daily\\nalert_iqvia_internal_monthly\\nalert_iqvia_internal_weekly\\nalignment_account_to_territory\\nalignment_account_to_territory_daily\\nalignment_account_to_territory_weekly\\nalignment_brand_credit_rules\\nalignment_brand_to_position\\nalignment_brand_to_position_daily\\nalignment_brand_to_position_weekly\\nalignment_brand_to_therapeutic_area\\nalignment_brand_to_therapeutic_area_daily\\nalignment_brand_to_therapeutic_area_weekly\\nalignment_ecosystem_dim\\nalignment_ecosystem_dim_daily\\nalignment_ecosystem_dim_weekly\\nalignment_ecosystem_hierarchy\\nalignment_ecosystem_hierarchy_daily\\nalignment_ecosystem_hierarchy_history_daily\\nalignment_ecosystem_hierarchy_weekly\\nalignment_employee_dim\\nalignment_employee_dim_daily\\nalignment_employee_dim_weekly\\nalignment_field_force_dim\\nalignment_field_force_dim_daily\\nalignment_field_force_dim_weekly\\nalignment_field_force_hierarchy\\nalignment_field_force_hierarchy_daily\\nalignment_field_force_hierarchy_weekly\\nalignment_hcp_outlet_credit_flag\\nalignment_hcp_outlet_credit_flag_weekly\\nalignment_hcp_outlet_exception_credit_flag\\nalignment_roster_hierarchy_daily\\nalignment_sales_area_relationship_asc\\nalignment_sales_area_relationship_asc_daily\\nalignment_sales_area_relationship_asc_weekly\\nalignment_sales_area_relationship_roster_daily\\nalignment_territory_hierarchy\\nalignment_territory_hierarchy_daily\\nalignment_territory_hierarchy_weekly\\nalignment_territory_therapeutic_area_allocation_daily\\nalignment_user_assignment_allocation_daily\\nalignment_user_to_field_title\\nalignment_user_to_field_title_daily\\nalignment_user_to_field_title_weekly\\nalignment_zip_to_ecosystem\\nalignment_zip_to_ecosystem_daily\\nalignment_zip_to_ecosystem_weekly\\nalignment_zip_to_position\\nalignment_zip_to_position_daily\\nalignment_zip_to_position_weekly\\nalignments_field_force_brand_asc\\nanonymized_pdv_oasis_voc_medical_patient_quest_resp_weekly\\nanonymized_pdv_synapse_inquiry\\nbenefit_map_brand_national\\nbioonc_soc_classification\\nbob_adjustment_factor_national\\nbob_mix_sales_national\\nbob_sales_appended_national\\nbob_sales_mix_ecosystem_national\\nbob_sales_mix_zip3_national\\nbrand_book_of_business\\nbrand_book_of_business_national\\nbrand_hcp_affiliations\\nbrand_ndc\\ncare_cem_hemlibra_interaction_hist\\ncare_cem_sp_list\\ncare_frm_pact_case_l\\ncare_hco_cot_sites\\ncare_hco_tier\\ncare_hcp_sma_fao\\ncdm_brnd_to_ther_area\\ncdm_emp_dim\\nclaim_split\\nclinical_trials_normalized\\ncrm_account\\ncrm_account_address\\ncrm_medical_approved_email\\ncrm_medical_event_based_customer_list\\ncrm_medical_inquiry_request\\ncrm_medical_insights\\ncrm_medical_interactions\\ncrm_medical_priority_customer_list\\ncrm_medical_standing_requests\\ncrm_medical_survey\\ncrm_medical_territory_sales_force\\ncrm_user_profile\\ndrg_pbm_zip_lives\\ndrg_spcm_pbm_entity_bridge\\necosystem_dim\\necosystem_dim_weekly\\necosystem_hier\\nfba_sap_outs_fact\\nfinance_brand_ndc_eff_wac_price_monthly\\nfinance_ecosystem_actual_vs_forecast\\nfinance_ecosystem_actual_vs_forecast_2021\\nfinance_ecosystem_gross_and_net_actuals\\nfinance_ecosystem_pnl\\nfinance_uslt_pnl\\ng2o_payer_iqvia_idm_sales_monthly\\nhcp360_activities\\nhcp360_activities_content\\nhcp360_activities_lftest\\nhcp360_profile_addresses\\nhcp360_profile_identifier_communication\\nhcp360_profile_relationships\\nhcp360_qualification_config\\nhcp_omni_clinical_trials_acronym_bimonthly\\nhcp_omni_clinical_trials_acronym_bimonthly_outbound\\nhcp_omni_clinical_trials_acronym_feedback_bimonthly_outbound\\nhcp_omni_clinical_trials_acronym_response_bimonthly\\nhcp_omni_clinical_trials_bimonthly\\nhcp_omni_clinical_trials_bimonthly_outbound\\nhcp_omni_medical_insights_daily\\nhcp_omni_medical_insights_daily_outbound\\nhcp_omni_medical_interaction_discussions_daily\\nhcp_omni_medical_interaction_discussions_daily_outbound\\nhcp_omni_medical_interactions_daily\\nhcp_omni_medical_interactions_daily_outbound\\nhcp_omni_medical_standing_request_daily\\nhcp_omni_medical_standing_request_daily_outbound\\nhcp_omni_medical_survey_daily\\nhcp_omni_medical_survey_daily_outbound\\nhcp_omni_voc_medallia_medical_daily\\nhcp_omni_voc_medallia_medical_daily_outbound\\nhcp_outlet_affiliations\\nhcp_outlet_affiliations_bimonthly\\nhcp_outlet_affiliations_weekly\\nhcp_outlet_alignments\\nhcp_outlet_alignments_weekly\\nhcp_outlet_field_force_alignments\\nhcp_outlet_field_force_alignments_weekly\\nhealth_outcomes\\nhealthcare_demographics_by_geography\\nidn_provider_site_sales_national\\nincidence_prevalence_by_indication\\nincidence_prevalence_by_indication_historical_2024\\ninteraction\\ninterim_ra_mean_adjusted_group_lvl\\ninterim_ra_nsp_adjusted_weight\\niqvia_alerts_crosswalk_weekly\\niqvia_alerts_metric_crosswalk_weekly\\niqvia_alerts_metric_weekly\\niqvia_alerts_weekly\\niqvia_hoa_crosswalk_monthly\\niqvia_restricted_outlets\\niqvia_restricted_outlets_staging\\nkol_ecosystem_classification\\nlives_fact\\nmarket_share_split_factor_monthly\\nmaster_cm_mdm_profile\\nmaster_cm_mdm_profile_bimonthly\\nmaster_cm_mdm_profile_weekly\\nmaster_cm_org_profile\\nmaster_cm_org_profile_weekly\\nmaster_cross_portfolio_mapping\\nmaster_metric_market_share_config\\nmaster_pm_brand\\nmaster_pm_brand_asc\\nmaster_pm_exception\\nmaster_pm_indication_asc\\nmaster_pm_indications\\nmaster_pm_product\\nmaster_pm_product_asc\\nmaster_pm_product_brand_report_asc\\nmaster_pm_product_indication_relationship\\nmaster_pm_product_xref\\nmaster_pm_scientific_area\\nmaster_therapeutic_area_mapping\\nmaster_twitter_conference_name_mapping\\nmaster_zip_geography_mapping\\nmaster_zip_geography_mapping_historical_2024\\nmdm_access_dim\\nmdm_mco_src_xref\\nmdm_payer_access_dim\\nmdm_payer_hierarchy\\nmdm_pbm_hierarchy\\nmdm_plan_hierarchy\\nmdm_plan_hierarchy_dim\\nmedical_activity_based_customer_list\\nmedical_congress_publication\\nmeeting_conventions_medical_daily\\nmeeting_conventions_tracker\\nnational_bob_mix_brand_rebate_adj\\nnorm_pct_data_refresh_details\\nnrm_mdm_geo\\noasis_10417_mdm_access_dim_uat\\noasis_payer_iqvia_bkp\\noasis_payer_iqvia_new\\noce_medical_inquiry_request\\noce_medical_insights\\noce_medical_interactions\\noce_medical_standing_requests\\noce_medical_survey\\nomni_patient_cohort_claims\\nomni_patient_lot_aggregate\\nomni_patient_lot_episode\\norg_alignments\\norg_alignments_weekly\\norg_field_force_alignments\\norg_field_force_alignments_weekly\\norg_lmt_assignment\\npact_hco_staging_history_weekly\\npact_hcp_affiliations_staging_history_weekly\\npayer_360_master_mco_profile\\npayer_360_mco_hierarchy\\npayer_formulary_disadvantage_weight\\npayer_formulary_policy_dim\\npayer_formulary_policy_dim_ahr\\npayer_formulary_policy_dim_ahr_12_11\\npayer_formulary_policy_dim_bkp\\npayer_formulary_policy_dim_history\\npayer_formulary_policy_lives\\npayer_iqvia_idm_sales_monthly\\npayer_iqvia_idm_sales_weekly\\npayer_lives_monthly\\npayer_mix_national\\npayer_override\\npayer_pbm_info_national\\npayer_policy_lives\\npayer_policy_lives_test\\npayer_segment_formulary_policy_dim\\npayer_source_formulary_policy_dim\\npayer_source_formulary_policy_dim_partition\\npbm_mapping_info_national\\npbm_payer_lives_monthly\\npct_careset_claims_data\\npct_spear_provider_patient\\npersona_hcp_profile_weekly\\npopulation_demographics\\nptnt_cf_brnd_fact\\npto_hpm_config\\npto_key_accounts\\npto_key_accounts_phesgo\\npto_market_basket\\npto_market_basket_ahr\\npto_optha_target_list\\npto_product_desc\\npto_target_flag\\npto_tec_analytical_brand\\nrts_characteristic_indication\\nrts_product_universe\\nrts_scientific_area\\nrts_tiered_indications\\nsite_medical_pharmacy_sales_national\\nsite_sales_brand_national_imputed_monthly\\nsite_sales_flag_national\\nsite_sales_monthly_national\\nsymphony_site_alerts_weekly\\nsynapse_hcp_inquiry\\nsynapse_medical_content\\nuser_to_ecosystem_hier\\nusma_ct_collaborator\\nusma_ct_indication\\nusma_ct_pi\\nusma_ct_product\\nusma_ct_site\\nusma_ct_sponsor_name_type\\nusma_ct_study_details\\nusma_ct_study_site\\nveeva_link_address_weekly\\nveeva_link_association_weekly\\nveeva_link_clinical_leader_weekly\\nveeva_link_clinical_trial_weekly\\nveeva_link_company_collaboration_weekly\\nveeva_link_company_sponsorship_weekly\\nveeva_link_digital_reach_focus_area_weekly\\nveeva_link_event_attendee_weekly\\nveeva_link_field_favorites_weekly\\nveeva_link_focus_area_weekly\\nveeva_link_gene_link_mdm_match_qtrly\\nveeva_link_index_weekly\\nveeva_link_medical_event_weekly\\nveeva_link_payment_weekly\\nveeva_link_publication_weekly\\nveeva_link_scientific_activity_counts_weekly\\nveeva_link_scientific_expert_changelog_weekly\\nveeva_link_scientific_expert_clinical_trial_weekly\\nveeva_link_scientific_expert_publication_weekly\\nveeva_link_scientific_expert_weekly\\nveeva_link_scientificreach_mapping_weekly\\nvoc_anonymized_pdv_oasis_medical_patient_quest_resp_weekly\\nvoc_contact_center_synapse_daily\\nvoc_field_post_interaction_medical_daily\\nvoc_survey_attributes\\nwk_to_445_mth_dim\\nzip_to_dma'"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["table_names"]}, {"cell_type": "code", "execution_count": 52, "id": "e8f58a9d-363d-4fae-a257-114741660932", "metadata": {"tags": []}, "outputs": [], "source": ["SQL_PREFIX2=\"\"\"You are an agent designed to interact with a SQL database.\n", "Given an input question, create a syntactically correct AWS Athena query  to run, then look at the results of the query and return the answer.\n", "Unless the user specifies a specific number of examples / results they wish to obtain, always don't limit your number of results. Output all the results.\n", "You can order the results by a relevant column to return the most interesting examples in the database.\n", "Never query for all the columns from a specific table, only ask for the relevant columns given the question.\n", "You have access to tools for interacting with the database.\n", "Only use the below tools. Only use the information returned by the below tools to construct your final answer.\n", "You MUST double check your query before executing it. If you get an error while executing a query, rewrite the query and try again.\n", "\n", "Unless the user mentions specifically , always assume that user question is new and independent of previous question.\n", "Always use lower(table_name) before like for pattern matching. \n", "Do not add % in between words for pattern matching. like if user asks 'racial disparities' search for exact match '%racial disparities%'\n", "Always answer to the question in a structured format.\n", "\n", "Ignore your knowledge cutoff. Always consider all information from the table irrespective of its timestamp.\n", "If you don't know the answer, just say “I don't know”, don't try to make up an answer.\n", "\n", "DO NOT make any DML statements (INSERT, UPDATE, DELETE, DROP etc.) to the database.\n", "\n", "Return the names of ALL the SQL tables that MIGHT be relevant to the user question. \\\n", "The tables are:\n", "\n", "{table_names}\n", "\n", "Remember to include Only POTENTIALLY RELEVANT tables, even if you're not sure that they're needed.\n", "\n", "Do not include any tables that are not explicitly mentioned in the user question. Do not include all tables in the database. \n", "To start you should ALWAYS look at what tables in the database to see what you can query and which are relavent.\n", "Do NOT skip this step.\n", "Then you should query the schema of the most relevant tables. \n", "\n", "Give the answer such that even non technincal users can\n", "understand what the answer means. Don't include technical details like what queries have been run in the output.\n", "Don't include any 'Select * from' or  any select statements in the output /responses.\n", "It should be clear to non techical users too.  Just give output of the user question. No details about queries you \n", "ran against the user question needed. \n", "\"\"\"\n", "\n"]}, {"cell_type": "code", "execution_count": 53, "id": "9cd5a459-5de3-4a15-94bb-7d063476119f", "metadata": {"tags": []}, "outputs": [], "source": ["system_message = SystemMessage(content=SQL_PREFIX2)"]}, {"cell_type": "code", "execution_count": 54, "id": "8947d8c8-a659-41fd-bb8d-294cde65dc6d", "metadata": {"tags": []}, "outputs": [], "source": ["# agent_executor = create_react_agent(anthropic_llm, tools, messages_modifier=system_message)"]}, {"cell_type": "code", "execution_count": 55, "id": "ebb9b276-5773-488b-ae91-b23138eb0558", "metadata": {"tags": []}, "outputs": [], "source": ["dml_keywords=['update','delete','insert','drop','alter','create','remove','add','truncate','modify','replace',\n", "              'change','replace','clear']"]}, {"cell_type": "code", "execution_count": 56, "id": "853844f1-9e6f-444f-9b96-0d825c1c97d8", "metadata": {"tags": []}, "outputs": [], "source": ["dml_keywords1=['update','delete','insert','drop','alter','create','remove','truncate','modify','replace',\n", "              'change','replace','clear']"]}, {"cell_type": "code", "execution_count": 57, "id": "98f134b0-804d-48ee-9643-0b61b79fa2ea", "metadata": {"tags": []}, "outputs": [], "source": ["custom_css = \"\"\"\n", "#input-textbox {\n", "    color: red; /* Changes text color */\n", "    background-color: LightBlue; /* Changes background color */\n", "    border: 2px solid #00ccff; /* Changes border color */\n", "    font-size: 16px; /* Changes font size */\n", "    padding: 10px; /* Adds padding inside the textbox */\n", "}\n", "\n", "#output-textbox {\n", "    color: red; /* Changes text color */\n", "    background-color: LightGreen; /* Changes background color */\n", "    border: 2px solid #00ccff; /* Changes border color */\n", "    font-size: 16px; /* Changes font size */\n", "    padding: 10px; /* Adds padding inside the textbox */\n", "}\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 58, "id": "5cb0948e-6370-49ba-9174-de391f543c5a", "metadata": {"tags": []}, "outputs": [], "source": ["html_title = \"\"\"\n", "    <div style=\"text-align: center;\">\n", "        <h1>Chat with Medical Congress Publications Database</h1>\n", "    </div>\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 59, "id": "aed6075a-94ba-4aa0-860d-b3cea3c43fc0", "metadata": {"tags": []}, "outputs": [], "source": ["#Self consistency with Memory"]}, {"cell_type": "code", "execution_count": 60, "id": "2b2b29c9-eaa5-4c1c-b178-30a8e50a65fd", "metadata": {"tags": []}, "outputs": [], "source": ["def generate_random_string():\n", "    ascii_values = list(range(97, 121))\n", "    random_char = random.choice(ascii_values)\n", "    random_string = chr(random_char)+chr(random_char+1)+chr(random_char+2)\n", "    number_list=[1,2,3,4,5,6,7]\n", "    random_int = random.choice(number_list)\n", "    random_number = str(random_int)+str(random_int+1)+str(random_int+2)\n", "    return random_string+random_number"]}, {"cell_type": "code", "execution_count": 61, "id": "de28cecc-0a00-4a08-8df2-26f8fde7eeef", "metadata": {"tags": []}, "outputs": [], "source": ["config = {\"configurable\": {\"thread_id\": \"abc123\"}}"]}, {"cell_type": "code", "execution_count": 62, "id": "e691360e-4a97-4f4d-ae73-354e9319fc68", "metadata": {"tags": []}, "outputs": [], "source": ["template_self_consistency= \"\"\"\n", "considering the user question {user_question} and  Using the following answers, return the final resoonse which you as an sql expert consider as best out of all conidering its \n", "relevance to the question. Just give he final answer. No verbose is needed\n", "{answers}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 63, "id": "856a7a00-afe7-41d7-8edb-b1fe63b68189", "metadata": {"tags": []}, "outputs": [], "source": ["template_self_consistency1= \"\"\"\n", "considering the user question {user_question} and using the answers {answers}, return the final repsonse which you as an sql expert think as best by \n", "reviewing all the answers, then combining the most accurate and valuable information from each answer, then rephrasing and structuring the final output to ensure it is clear, concise, and logically organized, avoiding any redundancy. \n", "The goal is to produce a comprehensive response that captures the strengths of each individual answer by considering relevance to the question. Just give he final answer. No verbose is needed\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 64, "id": "7ae69194-7225-461e-bec3-06345f51ee4c", "metadata": {"tags": []}, "outputs": [], "source": ["#Review all three responses, and then combine the most accurate and valuable information from each. Rephrase and structure the final output to ensure it is clear, concise, and logically organized, avoiding any redundancy. The goal is to produce a comprehensive response that captures the strengths of each individual attempt."]}, {"cell_type": "code", "execution_count": 65, "id": "0847e2a9-5b77-40c6-9977-a1ca2da807b1", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["PromptTemplate(input_variables=['answers', 'user_question'], input_types={}, partial_variables={}, template='\\nconsidering the user question {user_question} and using the answers {answers}, return the final repsonse which you as an sql expert think as best by \\nreviewing all the answers, then combining the most accurate and valuable information from each answer, then rephrasing and structuring the final output to ensure it is clear, concise, and logically organized, avoiding any redundancy. \\nThe goal is to produce a comprehensive response that captures the strengths of each individual answer by considering relevance to the question. Just give he final answer. No verbose is needed\\n')"]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["consistency_prompt = PromptTemplate.from_template(template_self_consistency1)\n", "consistency_prompt"]}, {"cell_type": "code", "execution_count": 66, "id": "7fc49ffc-d526-470b-908e-2b6d50c1bae8", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["PromptTemplate(input_variables=['answers', 'user_question'], input_types={}, partial_variables={}, template='\\nconsidering the user question {user_question} and using the answers {answers}, return the final repsonse which you as an sql expert think as best by \\nreviewing all the answers, then combining the most accurate and valuable information from each answer, then rephrasing and structuring the final output to ensure it is clear, concise, and logically organized, avoiding any redundancy. \\nThe goal is to produce a comprehensive response that captures the strengths of each individual answer by considering relevance to the question. Just give he final answer. No verbose is needed\\n')\n", "| ChatBedrock(client=<botocore.client.BedrockRuntime object at 0x7fca65c4cbf0>, model_id='anthropic.claude-3-sonnet-20240229-v1:0', model_kwargs={'top_p': 0.95}, temperature=0.7, max_tokens=2048)"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["consistency_chain =  consistency_prompt | anthropic_llm\n", "\n", "consistency_chain"]}, {"cell_type": "code", "execution_count": 67, "id": "3bee8c93-c5a9-4a18-a1f3-a586843fa483", "metadata": {"tags": []}, "outputs": [], "source": ["memory = MemorySaver()"]}, {"cell_type": "code", "execution_count": 68, "id": "1ae5c462-4777-4ba7-8192-f3ec365f6414", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: langchain in /opt/conda/lib/python3.12/site-packages (0.3.23)\n", "Requirement already satisfied: langchain-core<1.0.0,>=0.3.51 in /opt/conda/lib/python3.12/site-packages (from langchain) (0.3.51)\n", "Requirement already satisfied: langchain-text-splitters<1.0.0,>=0.3.8 in /opt/conda/lib/python3.12/site-packages (from langchain) (0.3.8)\n", "Requirement already satisfied: langsmith<0.4,>=0.1.17 in /opt/conda/lib/python3.12/site-packages (from langchain) (0.2.11)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.7.4 in /opt/conda/lib/python3.12/site-packages (from langchain) (2.9.2)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /opt/conda/lib/python3.12/site-packages (from langchain) (2.0.40)\n", "Requirement already satisfied: requests<3,>=2 in /opt/conda/lib/python3.12/site-packages (from langchain) (2.32.3)\n", "Requirement already satisfied: PyYAML>=5.3 in /opt/conda/lib/python3.12/site-packages (from langchain) (6.0.2)\n", "Requirement already satisfied: tenacity!=8.4.0,<10.0.0,>=8.1.0 in /opt/conda/lib/python3.12/site-packages (from langchain-core<1.0.0,>=0.3.51->langchain) (9.0.0)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /opt/conda/lib/python3.12/site-packages (from langchain-core<1.0.0,>=0.3.51->langchain) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in /opt/conda/lib/python3.12/site-packages (from langchain-core<1.0.0,>=0.3.51->langchain) (24.2)\n", "Requirement already satisfied: typing-extensions>=4.7 in /opt/conda/lib/python3.12/site-packages (from langchain-core<1.0.0,>=0.3.51->langchain) (4.12.2)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /opt/conda/lib/python3.12/site-packages (from langsmith<0.4,>=0.1.17->langchain) (0.28.1)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /opt/conda/lib/python3.12/site-packages (from langsmith<0.4,>=0.1.17->langchain) (3.10.15)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in /opt/conda/lib/python3.12/site-packages (from langsmith<0.4,>=0.1.17->langchain) (1.0.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /opt/conda/lib/python3.12/site-packages (from pydantic<3.0.0,>=2.7.4->langchain) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.23.4 in /opt/conda/lib/python3.12/site-packages (from pydantic<3.0.0,>=2.7.4->langchain) (2.23.4)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in /opt/conda/lib/python3.12/site-packages (from requests<3,>=2->langchain) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in /opt/conda/lib/python3.12/site-packages (from requests<3,>=2->langchain) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /opt/conda/lib/python3.12/site-packages (from requests<3,>=2->langchain) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /opt/conda/lib/python3.12/site-packages (from requests<3,>=2->langchain) (2025.1.31)\n", "Requirement already satisfied: greenlet>=1 in /opt/conda/lib/python3.12/site-packages (from SQLAlchemy<3,>=1.4->langchain) (3.1.1)\n", "Requirement already satisfied: anyio in /opt/conda/lib/python3.12/site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain) (4.9.0)\n", "Requirement already satisfied: httpcore==1.* in /opt/conda/lib/python3.12/site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain) (1.0.7)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /opt/conda/lib/python3.12/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in /opt/conda/lib/python3.12/site-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.51->langchain) (3.0.0)\n", "Requirement already satisfied: sniffio>=1.1 in /opt/conda/lib/python3.12/site-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain) (1.3.1)\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install -U langchain"]}, {"cell_type": "code", "execution_count": 69, "id": "69d5fe45-6913-41c4-bf44-6af3d9db511a", "metadata": {"tags": []}, "outputs": [], "source": ["# from langchain.agents import create_react_agent\n", "# from langchain.memory import ConversationBufferMemory\n", "\n", "# # Assuming `anthropic_llm` is your LLM and `tools` is a list of tools\n", "# # memory = ConversationBufferMemory(memory_key=\"chat_history\", return_messages=True)\n", "# memory = MemorySaver()\n", "\n", "# agent_executor = create_react_agent(\n", "#     llm=anthropic_llm,\n", "#     tools=tools,\n", "#     messages_modifier=system_message,\n", "#     checkpointer=memory\n", "# )"]}, {"cell_type": "code", "execution_count": 70, "id": "7b57a22f-dd96-4324-8279-4cad2ba6701f", "metadata": {"tags": []}, "outputs": [], "source": ["# agent_executor = create_react_agent(anthropic_llm, tools,  messages_modifier=system_message,checkpointer=memory)\n", "agent_executor = create_react_agent(anthropic_llm, tools, prompt = system_message, checkpointer=memory)"]}, {"cell_type": "code", "execution_count": 71, "id": "e26778c0-ff5d-4ce5-b982-0158c5530431", "metadata": {"tags": []}, "outputs": [], "source": ["config = {\"configurable\": {\"thread_id\": generate_random_string()}}"]}, {"cell_type": "code", "execution_count": 72, "id": "9f08edb3-298f-4b4d-80fa-8d02d6d1c720", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["{'configurable': {'thread_id': 'klm456'}}"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["config"]}, {"cell_type": "code", "execution_count": 73, "id": "5584c9fd-44d3-4597-814e-6be742270ca8", "metadata": {"tags": []}, "outputs": [], "source": ["def ask_sql_with_memory_self_consistency_query(user_input,history):\n", "    i=0\n", "    if user_input.lower()=='bye' or user_input.lower()=='stop':\n", "        return \"Thank you for using my serice. Have a great day!\"\n", "    while(True):\n", "        if any(keyword in user_input.lower() for keyword in dml_keywords1):\n", "            return \"Dear user, Please don't ask questions that modify the database. Thank you.\"        \n", "        if user_input.lower()=='hi' or user_input.lower()=='hello':\n", "            return \"Hello! Hope you are doing well\"\n", "        if 'how are you' in user_input.lower():\n", "            return \"Dear user, I'm fine Thank You\"\n", "        try :\n", "            responses = []\n", "            query_outputs = set()\n", "            old_stdout = sys.stdout\n", "            for j in range(3):\n", "                mystdout = StringIO()\n", "                sys.stdout = mystdout\n", "                \n", "                res=agent_executor.invoke({\"messages\": [HumanMessage(content=user_input)]},config)\n", "                responses.append(res['messages'][-1].content)\n", "                \n", "                sys.stdout = old_stdout\n", "                captured_output = mystdout.getvalue()\n", "                captured_output=captured_output.replace('\\n\\n','\\n').replace('\\n', ' ').replace('  ',' ')\n", "                captured_output=captured_output.strip()\n", "                \n", "                \n", "                with open('temp_query.txt', 'w') as file:\n", "                    file.write(captured_output)\n", "                with open('temp_query.txt', 'r') as file:\n", "                    captured_output = file.read()\n", "\n", "                #print('query agent is running',captured_output)\n", "                query_outputs.add(captured_output.strip())                \n", "                \n", "                print('hi',query_outputs)\n", "                sys.stdout = mystdout\n", "            sys.stdout = old_stdout\n", "            answers = '\\n\\n'.join(responses)\n", "            final_res=consistency_chain.invoke({\"answers\":answers, 'user_question':user_input})\n", "            final_res=final_res.content\n", "            final_res='\\n'.join(item.strip() for item in final_res.replace('\\n\\n','\\n').split('\\n'))\n", "            with open('output.txt', 'w') as file:\n", "                file.write(final_res)\n", "            with open('output.txt', 'r') as file:\n", "                content = file.read()\n", "            llm_reponse=anthropic_llm.invoke(user_input).content\n", "            final_result='Agent Response is : '+'\\n'+list(query_outputs)[-1] + '\\n and \\n'+ content.replace('veeva_link_event_talk_table table', 'table').replace('veeva_link_event_talk_table', 'table')+'\\n\\n'+'LLM Response is : '+'\\n'+llm_reponse\n", "            with open('output_final.txt', 'w') as file:\n", "                file.write(final_result)\n", "            with open('output_final.txt', 'r') as file:\n", "                content_final = file.read()\n", "            return content_final\n", "        except Exception as e:\n", "            i=i+1\n", "            traceback_str = ''.join(traceback.format_exception(etype=type(e), value=e, tb=e.__traceback__))\n", "            print(f\"An error occurred: {e}\\nDetails:\\n{traceback_str}\")\n", "            #print(f\"An error occurred: {e}\")\n", "            if i==20:\n", "                return \"Sorry, I couldn't get results for your query this time. Please try again.\"\n", "                break\n", "            else:\n", "                continue"]}, {"cell_type": "code", "execution_count": 74, "id": "4daadb62-41d6-45bb-8adb-8ee64add451d", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* Running on local URL:  http://127.0.0.1:7860\n", "* Running on public URL: https://c13228e54d368d903b.gradio.live\n", "\n", "This share link expires in 72 hours. For free permanent hosting and GPU upgrades, run `gradio deploy` from the terminal in the working directory to deploy to Hugging Face Spaces (https://huggingface.co/spaces)\n"]}, {"data": {"text/html": ["<div><iframe src=\"https://c13228e54d368d903b.gradio.live\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": []}, "execution_count": 74, "metadata": {}, "output_type": "execute_result"}, {"name": "stderr", "output_type": "stream", "text": ["Error raised by bedrock service\n", "Traceback (most recent call last):\n", "  File \"/opt/conda/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 537, in _make_request\n", "    response = conn.getresponse()\n", "               ^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/urllib3/connection.py\", line 461, in getresponse\n", "    httplib_response = super().getresponse()\n", "                       ^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/http/client.py\", line 1430, in getresponse\n", "    response.begin()\n", "  File \"/opt/conda/lib/python3.12/http/client.py\", line 331, in begin\n", "    version, status, reason = self._read_status()\n", "                              ^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/http/client.py\", line 292, in _read_status\n", "    line = str(self.fp.readline(_MAXLINE + 1), \"iso-8859-1\")\n", "               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/socket.py\", line 720, in readinto\n", "    return self._sock.recv_into(b)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/ssl.py\", line 1251, in recv_into\n", "    return self.read(nbytes, buffer)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/ssl.py\", line 1103, in read\n", "    return self._sslobj.read(len, buffer)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "TimeoutError: The read operation timed out\n", "\n", "The above exception was the direct cause of the following exception:\n", "\n", "Traceback (most recent call last):\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/httpsession.py\", line 464, in send\n", "    urllib_response = conn.urlopen(\n", "                      ^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 845, in urlopen\n", "    retries = retries.increment(\n", "              ^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/urllib3/util/retry.py\", line 445, in increment\n", "    raise reraise(type(error), error, _stacktrace)\n", "          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/urllib3/util/util.py\", line 39, in reraise\n", "    raise value\n", "  File \"/opt/conda/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 791, in urlopen\n", "    response = self._make_request(\n", "               ^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 539, in _make_request\n", "    self._raise_timeout(err=e, url=url, timeout_value=read_timeout)\n", "  File \"/opt/conda/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 371, in _raise_timeout\n", "    raise ReadTimeoutError(\n", "urllib3.exceptions.ReadTimeoutError: AWSHTTPSConnectionPool(host='bedrock-runtime.us-west-2.amazonaws.com', port=443): Read timed out. (read timeout=60)\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/opt/conda/lib/python3.12/site-packages/langchain_aws/llms/bedrock.py\", line 935, in _prepare_input_and_invoke\n", "    response = self.client.invoke_model(**request_options)\n", "               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/client.py\", line 553, in _api_call\n", "    return self._make_api_call(operation_name, kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/client.py\", line 989, in _make_api_call\n", "    http, parsed_response = self._make_request(\n", "                            ^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/client.py\", line 1015, in _make_request\n", "    return self._endpoint.make_request(operation_model, request_dict)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/endpoint.py\", line 119, in make_request\n", "    return self._send_request(request_dict, operation_model)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/endpoint.py\", line 202, in _send_request\n", "    while self._needs_retry(\n", "          ^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/endpoint.py\", line 354, in _needs_retry\n", "    responses = self._event_emitter.emit(\n", "                ^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/hooks.py\", line 412, in emit\n", "    return self._emitter.emit(aliased_event_name, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/hooks.py\", line 256, in emit\n", "    return self._emit(event_name, kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/hooks.py\", line 239, in _emit\n", "    response = handler(**kwargs)\n", "               ^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/retryhandler.py\", line 207, in __call__\n", "    if self._checker(**checker_kwargs):\n", "       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/retryhandler.py\", line 284, in __call__\n", "    should_retry = self._should_retry(\n", "                   ^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/retryhandler.py\", line 320, in _should_retry\n", "    return self._checker(attempt_number, response, caught_exception)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/retryhandler.py\", line 363, in __call__\n", "    checker_response = checker(\n", "                       ^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/retryhandler.py\", line 247, in __call__\n", "    return self._check_caught_exception(\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/retryhandler.py\", line 416, in _check_caught_exception\n", "    raise caught_exception\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/endpoint.py\", line 281, in _do_get_response\n", "    http_response = self._send(request)\n", "                    ^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/endpoint.py\", line 377, in _send\n", "    return self.http_session.send(request)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/httpsession.py\", line 501, in send\n", "    raise ReadTimeoutError(endpoint_url=request.url, error=e)\n", "botocore.exceptions.ReadTimeoutError: Read timeout on endpoint URL: \"https://bedrock-runtime.us-west-2.amazonaws.com/model/anthropic.claude-3-sonnet-20240229-v1%3A0/invoke\"\n", "Traceback (most recent call last):\n", "  File \"/opt/conda/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 537, in _make_request\n", "    response = conn.getresponse()\n", "               ^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/urllib3/connection.py\", line 461, in getresponse\n", "    httplib_response = super().getresponse()\n", "                       ^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/http/client.py\", line 1430, in getresponse\n", "    response.begin()\n", "  File \"/opt/conda/lib/python3.12/http/client.py\", line 331, in begin\n", "    version, status, reason = self._read_status()\n", "                              ^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/http/client.py\", line 292, in _read_status\n", "    line = str(self.fp.readline(_MAXLINE + 1), \"iso-8859-1\")\n", "               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/socket.py\", line 720, in readinto\n", "    return self._sock.recv_into(b)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/ssl.py\", line 1251, in recv_into\n", "    return self.read(nbytes, buffer)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/ssl.py\", line 1103, in read\n", "    return self._sslobj.read(len, buffer)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "TimeoutError: The read operation timed out\n", "\n", "The above exception was the direct cause of the following exception:\n", "\n", "Traceback (most recent call last):\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/httpsession.py\", line 464, in send\n", "    urllib_response = conn.urlopen(\n", "                      ^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 845, in urlopen\n", "    retries = retries.increment(\n", "              ^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/urllib3/util/retry.py\", line 445, in increment\n", "    raise reraise(type(error), error, _stacktrace)\n", "          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/urllib3/util/util.py\", line 39, in reraise\n", "    raise value\n", "  File \"/opt/conda/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 791, in urlopen\n", "    response = self._make_request(\n", "               ^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 539, in _make_request\n", "    self._raise_timeout(err=e, url=url, timeout_value=read_timeout)\n", "  File \"/opt/conda/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 371, in _raise_timeout\n", "    raise ReadTimeoutError(\n", "urllib3.exceptions.ReadTimeoutError: AWSHTTPSConnectionPool(host='bedrock-runtime.us-west-2.amazonaws.com', port=443): Read timed out. (read timeout=60)\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/tmp/ipykernel_2176/2994264868.py\", line 20, in ask_sql_with_memory_self_consistency_query\n", "    res=agent_executor.invoke({\"messages\": [HumanMessage(content=user_input)]},config)\n", "        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/langgraph/pregel/__init__.py\", line 2718, in invoke\n", "    for chunk in self.stream(\n", "                 ^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/langgraph/pregel/__init__.py\", line 2356, in stream\n", "    for _ in runner.tick(\n", "             ^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/langgraph/prebuilt/chat_agent_executor.py\", line 745, in call_model\n", "    response = cast(AIMessage, model_runnable.invoke(state, config))\n", "                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/langchain_core/runnables/base.py\", line 3047, in invoke\n", "    input = context.run(step.invoke, input, config)\n", "            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/langchain_core/runnables/base.py\", line 5440, in invoke\n", "    return self.bound.invoke(\n", "           ^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/langchain_core/language_models/chat_models.py\", line 331, in invoke\n", "    self.generate_prompt(\n", "  File \"/opt/conda/lib/python3.12/site-packages/langchain_core/language_models/chat_models.py\", line 894, in generate_prompt\n", "    return self.generate(prompt_messages, stop=stop, callbacks=callbacks, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/langchain_core/language_models/chat_models.py\", line 719, in generate\n", "    self._generate_with_cache(\n", "  File \"/opt/conda/lib/python3.12/site-packages/langchain_core/language_models/chat_models.py\", line 960, in _generate_with_cache\n", "    result = self._generate(\n", "             ^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/langchain_aws/chat_models/bedrock.py\", line 727, in _generate\n", "    completion, tool_calls, llm_output = self._prepare_input_and_invoke(\n", "                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/langchain_aws/llms/bedrock.py\", line 950, in _prepare_input_and_invoke\n", "    raise e\n", "  File \"/opt/conda/lib/python3.12/site-packages/langchain_aws/llms/bedrock.py\", line 935, in _prepare_input_and_invoke\n", "    response = self.client.invoke_model(**request_options)\n", "               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/client.py\", line 553, in _api_call\n", "    return self._make_api_call(operation_name, kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/client.py\", line 989, in _make_api_call\n", "    http, parsed_response = self._make_request(\n", "                            ^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/client.py\", line 1015, in _make_request\n", "    return self._endpoint.make_request(operation_model, request_dict)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/endpoint.py\", line 119, in make_request\n", "    return self._send_request(request_dict, operation_model)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/endpoint.py\", line 202, in _send_request\n", "    while self._needs_retry(\n", "          ^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/endpoint.py\", line 354, in _needs_retry\n", "    responses = self._event_emitter.emit(\n", "                ^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/hooks.py\", line 412, in emit\n", "    return self._emitter.emit(aliased_event_name, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/hooks.py\", line 256, in emit\n", "    return self._emit(event_name, kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/hooks.py\", line 239, in _emit\n", "    response = handler(**kwargs)\n", "               ^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/retryhandler.py\", line 207, in __call__\n", "    if self._checker(**checker_kwargs):\n", "       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/retryhandler.py\", line 284, in __call__\n", "    should_retry = self._should_retry(\n", "                   ^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/retryhandler.py\", line 320, in _should_retry\n", "    return self._checker(attempt_number, response, caught_exception)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/retryhandler.py\", line 363, in __call__\n", "    checker_response = checker(\n", "                       ^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/retryhandler.py\", line 247, in __call__\n", "    return self._check_caught_exception(\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/retryhandler.py\", line 416, in _check_caught_exception\n", "    raise caught_exception\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/endpoint.py\", line 281, in _do_get_response\n", "    http_response = self._send(request)\n", "                    ^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/endpoint.py\", line 377, in _send\n", "    return self.http_session.send(request)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/botocore/httpsession.py\", line 501, in send\n", "    raise ReadTimeoutError(endpoint_url=request.url, error=e)\n", "botocore.exceptions.ReadTimeoutError: Read timeout on endpoint URL: \"https://bedrock-runtime.us-west-2.amazonaws.com/model/anthropic.claude-3-sonnet-20240229-v1%3A0/invoke\"\n", "During task with name 'agent' and id 'f4ff52aa-08e1-0e31-2a74-a319f89e0ba5'\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/opt/conda/lib/python3.12/site-packages/gradio/queueing.py\", line 624, in process_events\n", "    response = await route_utils.call_process_api(\n", "               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/gradio/route_utils.py\", line 323, in call_process_api\n", "    output = await app.get_blocks().process_api(\n", "             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/gradio/blocks.py\", line 2015, in process_api\n", "    result = await self.call_function(\n", "             ^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/gradio/blocks.py\", line 1560, in call_function\n", "    prediction = await fn(*processed_input)\n", "                 ^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/gradio/utils.py\", line 832, in async_wrapper\n", "    response = await f(*args, **kwargs)\n", "               ^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/gradio/chat_interface.py\", line 649, in _submit_fn\n", "    response = await anyio.to_thread.run_sync(\n", "               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/anyio/to_thread.py\", line 56, in run_sync\n", "    return await get_async_backend().run_sync_in_worker_thread(\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/anyio/_backends/_asyncio.py\", line 2470, in run_sync_in_worker_thread\n", "    return await future\n", "           ^^^^^^^^^^^^\n", "  File \"/opt/conda/lib/python3.12/site-packages/anyio/_backends/_asyncio.py\", line 967, in run\n", "    result = context.run(func, *args)\n", "             ^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/tmp/ipykernel_2176/2994264868.py\", line 57, in ask_sql_with_memory_self_consistency_query\n", "    traceback_str = ''.join(traceback.format_exception(etype=type(e), value=e, tb=e.__traceback__))\n", "                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "TypeError: format_exception() got an unexpected keyword argument 'etype'\n"]}], "source": ["gr.<PERSON><PERSON>(\n", "    fn=ask_sql_with_memory_self_consistency_query,\n", "    chatbot=gr.<PERSON><PERSON>(height=300),\n", "    textbox=gr.Textbox(placeholder=\"Hello... Drop Your Question here\", container=False, scale=7,elem_id=\"input-textbox\"),\n", "    css=custom_css,\n", "   title=\"<div style='text-align: center; color: #4A90E2; font-size: 1em;'>Chat with Veevalink Event Talk Table</div>\",\n", " #title=\"Chat with Medical Congress Publications Table\",\n", "    #title=html_title,\n", "    description=\"<div style='text-align: left; color: OrangeRed; font-size: 1em;'>Ask me any question about our table. If you feel my responses are not as expected, please ask the question again Or let's restart our conversation</div>\",\n", "    theme=\"soft\",\n", "  # examples=[\"which titles contain health equity?\",\"which authors mentioned racial disparities?\",\"Which indication has the highest average performed_patient_count in May 2024?\"],\n", "    cache_examples=False,\n", " #  retry_btn=\"🔄 Retry\",\n", "  #  undo_btn=\"↩️ Undo\",\n", "  #  clear_btn=\"🗑️ Clear\",\n", ").launch(share=True)"]}, {"cell_type": "markdown", "id": "aa753520-8a15-4dd7-a249-35757083d5af", "metadata": {"tags": []}, "source": ["# "]}, {"cell_type": "code", "execution_count": null, "id": "60a70c4c-f5dd-43d0-ae70-7bc7acbe7561", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3e4401ce-ae00-4f2b-99f1-1d8fd6bcd22b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1a584a3d-7944-45c2-9fa9-31395f21af3c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "43b3b4df-1926-45d5-82b6-8c1956ca3ad9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"availableInstances": [{"_defaultOrder": 0, "_isFastLaunch": true, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 4, "name": "ml.t3.medium", "vcpuNum": 2}, {"_defaultOrder": 1, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 8, "name": "ml.t3.large", "vcpuNum": 2}, {"_defaultOrder": 2, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.t3.xlarge", "vcpuNum": 4}, {"_defaultOrder": 3, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.t3.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 4, "_isFastLaunch": true, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 8, "name": "ml.m5.large", "vcpuNum": 2}, {"_defaultOrder": 5, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.m5.xlarge", "vcpuNum": 4}, {"_defaultOrder": 6, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.m5.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 7, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 64, "name": "ml.m5.4xlarge", "vcpuNum": 16}, {"_defaultOrder": 8, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 128, "name": "ml.m5.8xlarge", "vcpuNum": 32}, {"_defaultOrder": 9, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 192, "name": "ml.m5.12xlarge", "vcpuNum": 48}, {"_defaultOrder": 10, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 256, "name": "ml.m5.16xlarge", "vcpuNum": 64}, {"_defaultOrder": 11, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 384, "name": "ml.m5.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 12, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 8, "name": "ml.m5d.large", "vcpuNum": 2}, {"_defaultOrder": 13, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.m5d.xlarge", "vcpuNum": 4}, {"_defaultOrder": 14, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.m5d.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 15, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 64, "name": "ml.m5d.4xlarge", "vcpuNum": 16}, {"_defaultOrder": 16, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 128, "name": "ml.m5d.8xlarge", "vcpuNum": 32}, {"_defaultOrder": 17, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 192, "name": "ml.m5d.12xlarge", "vcpuNum": 48}, {"_defaultOrder": 18, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 256, "name": "ml.m5d.16xlarge", "vcpuNum": 64}, {"_defaultOrder": 19, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 384, "name": "ml.m5d.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 20, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": true, "memoryGiB": 0, "name": "ml.geospatial.interactive", "supportedImageNames": ["sagemaker-geospatial-v1-0"], "vcpuNum": 0}, {"_defaultOrder": 21, "_isFastLaunch": true, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 4, "name": "ml.c5.large", "vcpuNum": 2}, {"_defaultOrder": 22, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 8, "name": "ml.c5.xlarge", "vcpuNum": 4}, {"_defaultOrder": 23, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.c5.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 24, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.c5.4xlarge", "vcpuNum": 16}, {"_defaultOrder": 25, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 72, "name": "ml.c5.9xlarge", "vcpuNum": 36}, {"_defaultOrder": 26, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 96, "name": "ml.c5.12xlarge", "vcpuNum": 48}, {"_defaultOrder": 27, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 144, "name": "ml.c5.18xlarge", "vcpuNum": 72}, {"_defaultOrder": 28, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 192, "name": "ml.c5.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 29, "_isFastLaunch": true, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.g4dn.xlarge", "vcpuNum": 4}, {"_defaultOrder": 30, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.g4dn.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 31, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 64, "name": "ml.g4dn.4xlarge", "vcpuNum": 16}, {"_defaultOrder": 32, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 128, "name": "ml.g4dn.8xlarge", "vcpuNum": 32}, {"_defaultOrder": 33, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 4, "hideHardwareSpecs": false, "memoryGiB": 192, "name": "ml.g4dn.12xlarge", "vcpuNum": 48}, {"_defaultOrder": 34, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 256, "name": "ml.g4dn.16xlarge", "vcpuNum": 64}, {"_defaultOrder": 35, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 61, "name": "ml.p3.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 36, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 4, "hideHardwareSpecs": false, "memoryGiB": 244, "name": "ml.p3.8xlarge", "vcpuNum": 32}, {"_defaultOrder": 37, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 8, "hideHardwareSpecs": false, "memoryGiB": 488, "name": "ml.p3.16xlarge", "vcpuNum": 64}, {"_defaultOrder": 38, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 8, "hideHardwareSpecs": false, "memoryGiB": 768, "name": "ml.p3dn.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 39, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.r5.large", "vcpuNum": 2}, {"_defaultOrder": 40, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.r5.xlarge", "vcpuNum": 4}, {"_defaultOrder": 41, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 64, "name": "ml.r5.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 42, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 128, "name": "ml.r5.4xlarge", "vcpuNum": 16}, {"_defaultOrder": 43, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 256, "name": "ml.r5.8xlarge", "vcpuNum": 32}, {"_defaultOrder": 44, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 384, "name": "ml.r5.12xlarge", "vcpuNum": 48}, {"_defaultOrder": 45, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 512, "name": "ml.r5.16xlarge", "vcpuNum": 64}, {"_defaultOrder": 46, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 768, "name": "ml.r5.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 47, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.g5.xlarge", "vcpuNum": 4}, {"_defaultOrder": 48, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.g5.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 49, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 64, "name": "ml.g5.4xlarge", "vcpuNum": 16}, {"_defaultOrder": 50, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 128, "name": "ml.g5.8xlarge", "vcpuNum": 32}, {"_defaultOrder": 51, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 256, "name": "ml.g5.16xlarge", "vcpuNum": 64}, {"_defaultOrder": 52, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 4, "hideHardwareSpecs": false, "memoryGiB": 192, "name": "ml.g5.12xlarge", "vcpuNum": 48}, {"_defaultOrder": 53, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 4, "hideHardwareSpecs": false, "memoryGiB": 384, "name": "ml.g5.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 54, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 8, "hideHardwareSpecs": false, "memoryGiB": 768, "name": "ml.g5.48xlarge", "vcpuNum": 192}, {"_defaultOrder": 55, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 8, "hideHardwareSpecs": false, "memoryGiB": 1152, "name": "ml.p4d.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 56, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 8, "hideHardwareSpecs": false, "memoryGiB": 1152, "name": "ml.p4de.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 57, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.trn1.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 58, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 512, "name": "ml.trn1.32xlarge", "vcpuNum": 128}, {"_defaultOrder": 59, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 512, "name": "ml.trn1n.32xlarge", "vcpuNum": 128}], "instance_type": "ml.t3.medium", "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}
# Import necessary libraries from the notebook
from typing import TypedDict, List, Annotated, Union, Dict, Any
from typing_extensions import TypedDict
import random
import string
import sys
from io import StringIO

# Lang<PERSON>hain imports
from langchain_core.messages import HumanMessage, AIMessage, ToolMessage, SystemMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_aws.chat_models import ChatBedrock
from langchain_community.tools.sql_database.tool import QuerySQLDataBaseTool
from langchain_community.utilities import SQLDatabase
from langchain_core.output_parsers import StrOutputParser

# LangGraph imports
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

# Gradio for UI
import gradio as gr

# Helper function from the notebook
def generate_random_string(length=10):
    return ''.join(random.choices(string.ascii_letters + string.digits, k=length))

# Define a simple ToolExecutor class since we can't import it
class ToolExecutor:
    def __init__(self, tools):
        self.tools = {}
        for tool in tools:
            self.tools[tool.name] = tool
    
    def invoke(self, tool_invocation):
        tool_name = tool_invocation["name"]
        tool_input = tool_invocation["arguments"]
        
        if tool_name not in self.tools:
            raise ValueError(f"Tool {tool_name} not found. Available tools: {list(self.tools.keys())}")
        
        tool = self.tools[tool_name]
        # Handle different tool types appropriately
        if hasattr(tool, 'invoke'):
            return tool.invoke(tool_input)
        elif hasattr(tool, 'run'):
            return tool.run(tool_input)
        else:
            raise ValueError(f"Tool {tool_name} does not have invoke or run method")

# Define the state schema for our graph
class AgentState(TypedDict):
    messages: Annotated[List[Union[HumanMessage, AIMessage, ToolMessage, SystemMessage]], "Messages in the conversation"]

# Use the existing system prompt from the notebook
SQL_PREFIX = """You are an agent designed to interact with a SQL database.
Given an input question, create a syntactically correct AWS Athena query to run, then look at the results of the query and return the answer.
Unless the user specifies a specific number of examples / results they wish to obtain, always don't limit your number of results. Output all the results.
You can order the results by a relevant column to return the most interesting examples in the database.
Never query for all the columns from a specific table, only ask for the relevant columns given the question.
You have access to tools for interacting with the database.
"""

# Function to create a LangGraph SQL agent using the existing database and LLM
def create_sql_langgraph_agent(db, anthropic_llm, tools):
    # Create a tool executor
    tool_executor = ToolExecutor(tools)
    
    # Create the system message
    system_message = SystemMessage(content=SQL_PREFIX)
    
    # Define the agent node
    def agent_node(state: AgentState) -> Dict[str, Any]:
        """This node calls the LLM with the current messages and available tools."""
        messages = state["messages"]
        
        # If this is the first message, add the system message
        if len(messages) == 1 and isinstance(messages[0], HumanMessage):
            messages = [system_message] + messages
        
        # Call the LLM with the messages and tools
        response = anthropic_llm.bind(tools=tools).invoke(messages)
        
        # Return the response
        return {"messages": [response]}
    
    # Define the tool node
    def tool_node(state: AgentState) -> Dict[str, Any]:
        """This node executes any tool calls in the last message."""
        messages = state["messages"]
        last_message = messages[-1]
        
        if not isinstance(last_message, AIMessage) or not last_message.tool_calls:
            return {"messages": []}
        
        # Process each tool call
        tool_messages = []
        for tool_call in last_message.tool_calls:
            try:
                # Execute the tool
                response = tool_executor.invoke({
                    "name": tool_call["name"],
                    "arguments": tool_call["args"]
                })
                
                # Create a tool message
                tool_message = ToolMessage(
                    content=str(response),
                    tool_call_id=tool_call["id"],
                )
                tool_messages.append(tool_message)
            except Exception as e:
                # Handle errors in tool execution
                tool_message = ToolMessage(
                    content=f"Error executing tool: {str(e)}",
                    tool_call_id=tool_call["id"],
                )
                tool_messages.append(tool_message)
        
        return {"messages": tool_messages}
    
    # Define the conditional edge
    def should_continue(state: AgentState) -> str:
        """This function determines whether to continue the loop or end it."""
        messages = state["messages"]
        last_message = messages[-1]
        
        # If the last message is from the AI and has tool calls, continue to the tool node
        if isinstance(last_message, AIMessage) and last_message.tool_calls:
            return "tool_node"
        
        # Otherwise, end the graph
        return END
    
    # Create the graph
    workflow = StateGraph(AgentState)
    workflow.add_node("agent_node", agent_node)
    workflow.add_node("tool_node", tool_node)
    
    # Add the edges
    workflow.set_entry_point("agent_node")
    workflow.add_conditional_edges("agent_node", should_continue)
    workflow.add_edge("tool_node", "agent_node")
    
    # Compile the graph with increased recursion limit
    memory = MemorySaver()
    graph = workflow.compile(checkpointer=memory, recursion_limit=50)
    
    return graph

# Function to run the agent with self-consistency
def run_sql_agent_with_consistency(user_input, db, anthropic_llm, tools, consistency_chain):
    # Create the LangGraph agent
    graph = create_sql_langgraph_agent(db, anthropic_llm, tools)
    
    # Run multiple times for self-consistency
    responses = []
    query_outputs = set()
    
    # Capture stdout to get query details
    old_stdout = sys.stdout
    
    for j in range(3):  # Run 3 times as in the notebook
        mystdout = StringIO()
        sys.stdout = mystdout
        
        # Create a unique thread ID for this run
        thread_id = generate_random_string()
        config = {"configurable": {"thread_id": thread_id}, "recursion_limit": 50}
        
        # Run the agent
        res = graph.invoke({"messages": [HumanMessage(content=user_input)]}, config)
        responses.append(res['messages'][-1].content)
        
        # Restore stdout and capture output
        sys.stdout = old_stdout
        captured_output = mystdout.getvalue()
        captured_output = captured_output.replace('\n\n', '\n').replace('\n', ' ').replace('  ', ' ')
        captured_output = captured_output.strip()
        
        # Save captured output
        with open('temp_query.txt', 'w') as file:
            file.write(captured_output)
        with open('temp_query.txt', 'r') as file:
            captured_output = file.read()
            
        query_outputs.add(captured_output.strip())
        
        print('hi', query_outputs)
        sys.stdout = mystdout
    
    # Restore stdout
    sys.stdout = old_stdout
    
    # Combine results using consistency chain
    answers = '\n\n'.join(responses)
    final_res = consistency_chain.invoke({"answers": answers, 'user_question': user_input})
    final_res = final_res.content
    final_res = '\n'.join(item.strip() for item in final_res.replace('\n\n', '\n').split('\n'))
    
    # Save final result
    with open('output.txt', 'w') as file:
        file.write(final_res)
    with open('output.txt', 'r') as file:
        content = file.read()
    
    # Get direct LLM response for comparison
    llm_response = anthropic_llm.invoke(user_input).content
    
    # Format final output
    final_result = 'Agent Response is : ' + '\n' + list(query_outputs)[-1] + '\n and \n' + content.replace('veeva_link_event_talk_table table', 'table').replace('veeva_link_event_talk_table', 'table') + '\n\n' + 'LLM Response is : ' + '\n' + llm_response
    
    with open('output_final.txt', 'w') as file:
        file.write(final_result)
    
    return final_result

# Function to integrate with Gradio
def ask_sql_with_langgraph(user_input, db, anthropic_llm, tools, consistency_chain):
    try:
        result = run_sql_agent_with_consistency(user_input, db, anthropic_llm, tools, consistency_chain)
        return result
    except Exception as e:
        # Fix the traceback formatting
        import traceback
        error_msg = f"An error occurred: {str(e)}\n"
        error_msg += traceback.format_exc()
        return error_msg

# Main function to set up the Gradio interface
def setup_gradio_interface(db, anthropic_llm, tools, consistency_chain, custom_css):
    # Create the Gradio interface
    interface = gr.ChatInterface(
        fn=lambda user_input: ask_sql_with_langgraph(user_input, db, anthropic_llm, tools, consistency_chain),
        chatbot=gr.Chatbot(height=300),
        textbox=gr.Textbox(placeholder="Hello... Drop Your Question here", container=False, scale=7, elem_id="input-textbox"),
        css=custom_css,
        title="<div style='text-align: center; color: #4A90E2; font-size: 1em;'>Chat with Veevalink Event Talk Table</div>",
        description="<div style='text-align: left; color: OrangeRed; font-size: 1em;'>Ask me any question about our table. If you feel my responses are not as expected, please ask the question again Or let's restart our conversation</div>",
    )
    
    return interface

# Usage example (to be replaced with actual notebook code)
"""
# In your notebook, use this code:

# Fix the boto3 client issue by specifying request_checksum_calculation
from botocore.config import Config
boto_config = Config(
    region_name="us-west-2",
    request_checksum_calculation="when_supported"  # This fixes the InvalidChecksumConfigError
)

# Use the existing boto3 client
boto3_bedrock = boto3.client("bedrock-runtime", config=boto_config)

# Create the LangGraph agent with your existing tools and LLM
interface = setup_gradio_interface(db, anthropic_llm, tools, consistency_chain, custom_css)

# Launch the interface
interface.launch()
"""



